# 🚀 Server Deployment Guide

## Deployment Options

### 🏆 **Option 1: VPS/Cloud Server (Recommended)**

**Best providers:**
- **DigitalOcean**: $5/month droplet
- **Linode**: $5/month VPS
- **Vultr**: $5/month VPS
- **AWS EC2**: t2.micro (free tier)
- **Google Cloud**: e2-micro (free tier)

**Steps:**
1. **Create server** (Ubuntu 20.04/22.04)
2. **Upload your bot files** via SCP/SFTP
3. **Run deployment script**:
   ```bash
   chmod +x deploy_server.sh
   ./deploy_server.sh
   ```
4. **Start the bot**:
   ```bash
   sudo systemctl start valyuta-bot
   sudo systemctl status valyuta-bot
   ```

### 🆓 **Option 2: Free Hosting**

#### **Railway.app** (Recommended Free Option)
1. Connect GitHub account
2. Push your code to GitHub
3. Deploy on Railway
4. Add environment variables in Railway dashboard

#### **Render.com**
1. Connect GitHub repository
2. Create new Web Service
3. Add environment variables
4. Deploy automatically

#### **PythonAnywhere** (Free tier)
1. Upload files to PythonAnywhere
2. Create virtual environment
3. Run bot in "Always On Tasks"

### 🔧 **Option 3: Docker Deployment**

**Dockerfile:**
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "bot.py"]
```

**Deploy:**
```bash
docker build -t valyuta-bot .
docker run -d --name valyuta-bot --env-file .env valyuta-bot
```

## 📋 **Quick Setup Commands**

### **For Ubuntu/Debian Server:**
```bash
# 1. Copy files to server
scp -r kurs_bot/ user@your-server-ip:/home/<USER>/

# 2. SSH to server
ssh user@your-server-ip

# 3. Setup
cd kurs_bot
chmod +x deploy_server.sh
./deploy_server.sh

# 4. Start bot
sudo systemctl start valyuta-bot
```

### **Manual Setup:**
```bash
# Install dependencies
sudo apt update
sudo apt install python3 python3-pip python3-venv -y

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install packages
pip install -r requirements.txt

# Run bot
python3 bot.py
```

## 🔍 **Monitoring Commands**

```bash
# Check bot status
sudo systemctl status valyuta-bot

# View logs
sudo journalctl -u valyuta-bot -f

# Restart bot
sudo systemctl restart valyuta-bot

# Stop bot
sudo systemctl stop valyuta-bot
```

## 🛡️ **Security Tips**

1. **Use non-root user** for running the bot
2. **Setup firewall**:
   ```bash
   sudo ufw enable
   sudo ufw allow ssh
   sudo ufw allow 443
   ```
3. **Keep system updated**:
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```
4. **Monitor logs** regularly
5. **Backup your .env file**

## 📱 **Testing Deployment**

After deployment, test your bot:

1. **Message your bot**: [@aha_valyuta_bot](https://t.me/aha_valyuta_bot)
2. **Send**: `/start`
3. **Check rate**: `/rate`
4. **Verify logs** show rate checking every 2 minutes

## 🆘 **Troubleshooting**

### Bot not starting:
```bash
# Check logs
sudo journalctl -u valyuta-bot -n 50

# Check file permissions
ls -la /home/<USER>/kurs_bot/

# Test manually
cd /home/<USER>/kurs_bot
source venv/bin/activate
python3 bot.py
```

### Rate fetching issues:
```bash
# Test rate scraper
python3 test_rate_scraper.py

# Check internet connectivity
ping bank.uz
ping cbu.uz
```

## 💰 **Cost Estimates**

- **Free options**: Railway, Render, PythonAnywhere free tiers
- **Paid VPS**: $5-10/month (more reliable)
- **Resource usage**: Very low (< 100MB RAM, minimal CPU)

## 🔄 **Auto-Updates**

To update the bot on server:
```bash
# Stop bot
sudo systemctl stop valyuta-bot

# Update code
cd /home/<USER>/kurs_bot
git pull  # if using git

# Update dependencies
source venv/bin/activate
pip install -r requirements.txt

# Start bot
sudo systemctl start valyuta-bot
```

Your bot will run 24/7 and automatically monitor exchange rates! 