#!/usr/bin/env python3
"""
Test script for multi-currency functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.rate_scraper import ExchangeRateScraper
from src.core.message_formatter import MessageFormatter
from src.utils.config import Config

def test_currency_info():
    """Test currency info retrieval"""
    print("🧪 Testing currency info...")
    
    currencies = Config.get_currency_list()
    print(f"Supported currencies: {currencies}")
    
    for currency_code in currencies:
        info = Config.get_currency_info(currency_code)
        if info:
            print(f"  {currency_code}: {info['emoji']} {info['name_uz']} ({info['name_en']})")
        else:
            print(f"  {currency_code}: ❌ No info found")
    print()

def test_rate_scraping():
    """Test rate scraping for multiple currencies"""
    print("🧪 Testing rate scraping...")
    
    scraper = ExchangeRateScraper(timeout=15)
    currencies = ['USD', 'EUR', 'RUB']  # Test a few currencies
    
    for currency_code in currencies:
        print(f"\n📊 Testing {currency_code}:")
        
        # Test individual rate scraping
        cbu_rate = scraper.get_cbu_rate(currency_code)
        if cbu_rate:
            print(f"  ✅ CBU rate: {cbu_rate['central_bank_rate']:,.0f} som")
        else:
            print(f"  ❌ CBU rate failed")
        
        bank_rate = scraper.get_bank_uz_rate(currency_code)
        if bank_rate:
            print(f"  ✅ Bank.uz rate: {bank_rate['central_bank_rate']:,.0f} som")
        else:
            print(f"  ❌ Bank.uz rate failed")
        
        # Test best rate
        best_rate = scraper.get_best_rate(currency_code)
        if best_rate:
            print(f"  ✅ Best rate: {best_rate['central_bank_rate']:,.0f} som (from {best_rate['source']})")
        else:
            print(f"  ❌ Best rate failed")
    
    # Test multiple currencies at once
    print(f"\n📊 Testing multiple currencies:")
    all_rates = scraper.get_multiple_currencies(currencies)
    
    for currency_code, rate_data in all_rates.items():
        if rate_data:
            print(f"  ✅ {currency_code}: {rate_data['central_bank_rate']:,.0f} som")
        else:
            print(f"  ❌ {currency_code}: Failed")
    print()

def test_message_formatting():
    """Test message formatting for different currencies"""
    print("🧪 Testing message formatting...")
    
    formatter = MessageFormatter()
    
    # Create sample rate data
    sample_rates = {
        'USD': {
            'currency': 'USD',
            'currency_info': Config.get_currency_info('USD'),
            'source': 'cbu.uz',
            'central_bank_rate': 12850.0,
            'timestamp': '2024-01-01T12:00:00'
        },
        'EUR': {
            'currency': 'EUR',
            'currency_info': Config.get_currency_info('EUR'),
            'source': 'cbu.uz',
            'central_bank_rate': 14200.0,
            'timestamp': '2024-01-01T12:00:00'
        },
        'RUB': {
            'currency': 'RUB',
            'currency_info': Config.get_currency_info('RUB'),
            'source': 'cbu.uz',
            'central_bank_rate': 145.5,
            'timestamp': '2024-01-01T12:00:00'
        }
    }
    
    # Test single currency formatting
    print("📝 Single currency format:")
    for currency_code, rate_data in sample_rates.items():
        message = formatter.format_rate_request(rate_data)
        print(f"\n{currency_code} Message Preview:")
        print("─" * 50)
        # Show first few lines
        lines = message.split('\n')[:4]
        for line in lines:
            print(line)
        print("...")
    
    # Test multiple currencies formatting
    print("\n📝 Multiple currencies format:")
    message = formatter.format_multiple_rates(sample_rates)
    print("Multiple Currencies Message Preview:")
    print("─" * 50)
    lines = message.split('\n')[:10]
    for line in lines:
        print(line)
    print("...")
    
    # Test currency selection formatting
    print("\n📝 Currency selection format:")
    message = formatter.format_currency_selection()
    print("Currency Selection Message Preview:")
    print("─" * 50)
    lines = message.split('\n')[:8]
    for line in lines:
        print(line)
    print("...")
    print()

def main():
    """Run all tests"""
    print("🚀 Testing Multi-Currency Bot Functionality\n")
    
    try:
        test_currency_info()
        test_rate_scraping()
        test_message_formatting()
        
        print("✅ All tests completed! The multi-currency functionality appears to be working.")
        print("\n💡 Next steps:")
        print("1. Update your bot token in config")
        print("2. Run the bot: python main.py")
        print("3. Try the new commands:")
        print("   • /currencies - Select currency")
        print("   • /rates - View all rates")
        print("   • /rate_usd, /rate_eur, etc. - Specific currency rates")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 