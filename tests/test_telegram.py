#!/usr/bin/env python3
"""
Test script to get your Chat ID for the bot configuration
"""

import asyncio
import sys
import os
from telegram import Bot

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.config import Config

async def get_chat_info():
    """Get information about who can receive messages from the bot"""
    
    if not Config.BOT_TOKEN:
        print("❌ BOT_TOKEN not found in environment variables")
        print("Make sure you have set BOT_TOKEN in your deployment environment")
        return
    
    bot = Bot(token=Config.BOT_TOKEN)
    
    try:
        # Get bot info
        me = await bot.get_me()
        print(f"✅ Bot connected: @{me.username}")
        print(f"Bot name: {me.first_name}")
        print(f"Bot ID: {me.id}")
        
        # Get updates to see who has messaged the bot
        print("\n📬 Getting recent messages...")
        updates = await bot.get_updates(limit=10)
        
        if not updates:
            print("\n⚠️  No recent messages found!")
            print("\n🔔 To fix the 'Chat not found' error:")
            print("1. Go to Telegram")
            print(f"2. Search for @{me.username}")
            print("3. Click START or send /start")
            print("4. Then run this script again to get your Chat ID")
            return
        
        print(f"\n📋 Found {len(updates)} recent messages:")
        chat_ids = set()
        
        for update in updates:
            if update.message:
                chat = update.message.chat
                user = update.message.from_user
                chat_ids.add(chat.id)
                
                print(f"\n👤 User: {user.first_name} (@{user.username or 'no_username'})")
                print(f"   Chat ID: {chat.id}")
                print(f"   Chat type: {chat.type}")
                print(f"   Message: {update.message.text[:50]}...")
        
        if chat_ids:
            print(f"\n✅ Available Chat IDs: {', '.join(map(str, chat_ids))}")
            print(f"\n🔧 To fix your bot configuration:")
            print(f"   Set CHAT_ID environment variable to one of these IDs")
            
            # Test sending a message
            for chat_id in chat_ids:
                try:
                    await bot.send_message(
                        chat_id=chat_id,
                        text="🧪 Test message from Chat ID finder script!"
                    )
                    print(f"✅ Successfully sent test message to {chat_id}")
                except Exception as e:
                    print(f"❌ Cannot send to {chat_id}: {e}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nPlease check your BOT_TOKEN is correct")

if __name__ == "__main__":
    print("🔍 Finding correct Chat ID for your bot...")
    print("=" * 50)
    asyncio.run(get_chat_info()) 