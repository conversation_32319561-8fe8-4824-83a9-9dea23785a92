#!/usr/bin/env python3
"""
Test script for the USD/UZS exchange rate scraper
"""

import sys
import os
import logging

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.rate_scraper import ExchangeRateScraper
from core.message_formatter import MessageFormatter

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

def test_rate_scraping():
    """Test rate scraping functionality"""
    print("🔍 Testing USD/UZS Rate Scraping...")
    print("=" * 50)
    
    scraper = ExchangeRateScraper()
    formatter = MessageFormatter()
    
    # Test bank.uz scraping
    print("\n📊 Testing bank.uz...")
    bank_uz_rate = scraper.get_bank_uz_rate()
    if bank_uz_rate:
        print("✅ bank.uz: Success")
        print(f"   Rate: {bank_uz_rate.get('central_bank_rate', 'N/A')} so'm")
        print(f"   Source: {bank_uz_rate.get('source', 'N/A')}")
    else:
        print("❌ bank.uz: Failed")
    
    # Test CBU API
    print("\n🏦 Testing CBU.uz...")
    cbu_rate = scraper.get_cbu_rate()
    if cbu_rate:
        print("✅ CBU.uz: Success")
        print(f"   Rate: {cbu_rate.get('central_bank_rate', 'N/A')} so'm")
        print(f"   Source: {cbu_rate.get('source', 'N/A')}")
    else:
        print("❌ CBU.uz: Failed")
    
    # Test best rate
    print("\n🎯 Testing best rate selection...")
    best_rate = scraper.get_best_rate()
    if best_rate:
        print("✅ Best rate: Success")
        print(f"   Rate: {best_rate.get('central_bank_rate', 'N/A')} so'm")
        print(f"   Source: {best_rate.get('source', 'N/A')}")
        
        # Test message formatting
        print("\n📝 Testing message formatting...")
        message = formatter.format_rate_request(best_rate)
        print("✅ Message formatted successfully")
        print("\nSample message:")
        print("-" * 30)
        print(message)
        
    else:
        print("❌ Best rate: Failed")
        print("⚠️  Could not fetch rates from any source")
    
    # Test all rates
    print("\n📋 Testing all rates...")
    all_rates = scraper.get_all_rates()
    print(f"✅ Found {len(all_rates)} rate source(s)")
    for i, rate in enumerate(all_rates, 1):
        print(f"   {i}. {rate.get('source', 'Unknown')}: {rate.get('central_bank_rate', 'N/A')} so'm")

def main():
    """Main test function"""
    try:
        test_rate_scraping()
        print("\n" + "=" * 50)
        print("🎉 Test completed successfully!")
        print("\nNext steps:")
        print("1. Create .env file with your BOT_TOKEN and CHAT_ID")
        print("2. Run: python bot.py")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 