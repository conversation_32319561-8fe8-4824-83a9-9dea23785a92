#!/data/data/com.termux/files/usr/bin/bash
# Test script for mobile multi-currency bot setup

echo "🧪 Testing Multi-Currency Bot Setup on Mobile..."
echo "=============================================="

# Check Python installation
echo "🐍 Python version:"
python3 --version || { echo "❌ Python3 not found"; exit 1; }

# Check required directories
echo ""
echo "📁 Checking directory structure:"
for dir in "src" "src/bot" "src/core" "src/utils" "tests"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir/ exists"
    else
        echo "❌ $dir/ missing"
    fi
done

# Check required files
echo ""
echo "📄 Checking required files:"
for file in "main.py" "src/bot/main.py" "src/core/rate_scraper.py" "src/core/message_formatter.py" "src/utils/config.py"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file missing"
    fi
done

# Check Python dependencies
echo ""
echo "📦 Checking Python dependencies:"
dependencies=("telegram" "requests" "bs4" "schedule" "dotenv" "aiohttp")
for dep in "${dependencies[@]}"; do
    if python3 -c "import $dep" 2>/dev/null; then
        echo "✅ $dep installed"
    else
        echo "❌ $dep missing"
    fi
done

# Check configuration
echo ""
echo "⚙️ Checking configuration:"
if [ -f ".env" ]; then
    echo "✅ .env file exists"
    if grep -q "BOT_TOKEN=your_bot_token_here" .env; then
        echo "⚠️  Please configure BOT_TOKEN in .env"
    else
        echo "✅ BOT_TOKEN configured"
    fi
else
    echo "❌ .env file missing"
fi

# Test import of main modules
echo ""
echo "🔧 Testing module imports:"
if python3 -c "
import sys
sys.path.append('src')
try:
    from bot.main import ExchangeRateBot
    from core.rate_scraper import RateScraper
    from core.message_formatter import MessageFormatter
    from utils.config import Config
    print('✅ All core modules import successfully')
    print(f'✅ Supported currencies: {len(Config.get_currency_list())}')
except Exception as e:
    print(f'❌ Import error: {e}')
    exit(1)
" 2>/dev/null; then
    echo "✅ Module imports successful"
else
    echo "❌ Module import failed"
fi

echo ""
echo "🎯 Test Summary:"
echo "==============="

# Count issues
issues=0
if [ ! -f "main.py" ]; then ((issues++)); fi
if [ ! -d "src" ]; then ((issues++)); fi
if [ ! -f ".env" ]; then ((issues++)); fi

if [ $issues -eq 0 ]; then
    echo "🎉 All tests passed! Mobile setup is ready."
    echo ""
    echo "Next steps:"
    echo "1. Configure BOT_TOKEN in .env file"
    echo "2. Run: ./start_mobile.sh"
    echo "3. Test bot with /start command"
else
    echo "⚠️  Found $issues issues. Please fix them before starting the bot."
fi

echo ""
echo "📱 Mobile-specific tips:"
echo "• Keep Termux app open to prevent Android from killing the process"
echo "• Use tmux sessions to run bot in background"
echo "• Monitor battery usage and optimize settings"
echo "• Use WiFi for better stability" 