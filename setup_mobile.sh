#!/data/data/com.termux/files/usr/bin/bash
# Mobile setup script for Multi-Currency Exchange Rate Bot
# Works on Termux (Android) and iSH (iOS)

echo "🤖 Setting up Multi-Currency Exchange Rate Bot for Mobile Server..."

# Update system packages
echo "📦 Updating packages..."
if command -v pkg &> /dev/null; then
    # Termux (Android)
    pkg update -y
    pkg install python git nano tmux -y
elif command -v apk &> /dev/null; then
    # iSH (iOS)
    apk update
    apk add python3 py3-pip git nano tmux
fi

# Install Python dependencies
echo "🐍 Installing Python dependencies..."
pip install --upgrade pip

# Use mobile-optimized requirements if available, otherwise use main requirements
if [ -f "requirements_mobile.txt" ]; then
    echo "📱 Using mobile-optimized requirements..."
    pip install -r requirements_mobile.txt
elif [ -f "requirements.txt" ]; then
    echo "📄 Using standard requirements..."
    pip install -r requirements.txt
else
    echo "⚠️ No requirements file found, installing basic dependencies..."
    pip install python-telegram-bot==21.9 requests==2.32.3 beautifulsoup4==4.12.3 schedule==1.2.2 python-dotenv==1.0.1 aiohttp==3.10.10
fi

# Check directory structure
echo "🔍 Checking bot structure..."
if [ ! -f "main.py" ]; then
    echo "❌ Error: main.py not found!"
    echo "Please make sure you're in the correct bot directory."
    exit 1
fi

if [ ! -d "src" ]; then
    echo "❌ Error: src/ directory not found!"
    echo "This script requires the new multi-currency bot structure."
    exit 1
fi

echo "✅ Bot structure verified"

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️ Creating configuration file..."
    cat > .env << EOF
# Multi-Currency Telegram Bot Configuration for Mobile Server

# Get this from @BotFather on Telegram
BOT_TOKEN=your_bot_token_here

# Your chat ID (get from @userinfobot) - Optional for multi-user bot
CHAT_ID=your_chat_id_here

# Rate check interval in seconds (default: 120 = 2 minutes)  
CHECK_INTERVAL=120

# Default currency for new users (USD, EUR, RUB, GBP, JPY, CHF)
DEFAULT_CURRENCY=USD
EOF
    echo "📝 Please edit .env file with your bot token:"
    echo "   nano .env"
else
    echo "✅ Configuration file already exists"
fi

# Create mobile-specific start script
cat > start_mobile.sh << 'EOF'
#!/data/data/com.termux/files/usr/bin/bash
# Mobile bot starter with session management

echo "🚀 Starting Multi-Currency Exchange Rate Bot on Mobile Server..."

# Check if already running
if tmux has-session -t botserver 2>/dev/null; then
    echo "⚠️ Bot session already exists!"
    echo "Options:"
    echo "  1. tmux attach -t botserver  (view running bot)"
    echo "  2. tmux kill-session -t botserver  (stop bot)"
    exit 1
fi

# Start bot in tmux session
tmux new-session -d -s botserver -c "$(pwd)"
tmux send-keys -t botserver "python3 main.py" Enter

echo "✅ Bot started in background session!"
echo "📱 Your phone is now a Telegram bot server!"
echo ""
echo "Useful commands:"
echo "  tmux attach -t botserver     # View bot logs"
echo "  tmux kill-session -t botserver  # Stop bot"
echo "  ./check_mobile.sh           # Check bot status"
echo ""
echo "💡 Keep Termux app open and prevent phone from sleeping for best performance"
EOF

# Create status check script
cat > check_mobile.sh << 'EOF'
#!/data/data/com.termux/files/usr/bin/bash
# Check mobile bot status

echo "📊 Mobile Bot Server Status"
echo "=========================="

# Check if session exists
if tmux has-session -t botserver 2>/dev/null; then
    echo "✅ Bot session: RUNNING"
    echo "📱 Server: Active on $(hostname)"
    echo "🔋 Battery optimization: Keep Termux open"
    echo ""
    echo "Recent logs:"
    tmux capture-pane -t botserver -p | tail -5
else
    echo "❌ Bot session: NOT RUNNING"
    echo "Start with: ./start_mobile.sh"
fi

echo ""
echo "📱 Device Info:"
echo "   Memory: $(cat /proc/meminfo | grep MemAvailable | awk '{print $2 " " $3}')"
echo "   Storage: $(df -h . | tail -1 | awk '{print $4 " available"}')"
EOF

# Make scripts executable
chmod +x start_mobile.sh check_mobile.sh setup_mobile.sh test_mobile.sh

echo ""
echo "🎉 Mobile setup complete!"
echo ""
echo "📊 Bot Features:"
echo "   • Multi-currency support (USD, EUR, RUB, GBP, JPY, CHF)"
echo "   • Per-currency notifications (users can choose which currencies to monitor)"
echo "   • Real-time rate monitoring from multiple sources"
echo "   • Personal currency preferences"
echo ""
echo "Next steps:"
echo "1. Edit configuration: nano .env"
echo "2. Add your bot token from @BotFather"
echo "3. Optionally set DEFAULT_CURRENCY (USD, EUR, RUB, GBP, JPY, CHF)"
echo "4. Test setup: ./test_mobile.sh"
echo "5. Start bot: ./start_mobile.sh"
echo "6. Check status: ./check_mobile.sh"
echo ""
echo "📱 Your phone will now act as a multi-currency Telegram bot server!"
echo "💡 Users can use /currencies to select preferred currency"
echo "🔔 Users can use /notifications to manage per-currency alerts" 