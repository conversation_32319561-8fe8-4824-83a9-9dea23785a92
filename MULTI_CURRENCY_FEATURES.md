# Multi-Currency Features

This bot now supports multiple currency pairs, not just USD/UZS. Users can select their preferred currency and get rates for various currencies.

## Supported Currencies

| Code | Currency | Emoji | Name (Uzbek) | Name (English) |
|------|----------|-------|--------------|----------------|
| USD  | $ | 💵 | AQSh dollari | US Dollar |
| EUR  | € | 💶 | Yevro | Euro |
| RUB  | ₽ | 🇷🇺 | Rossiya rubli | Russian Ruble |
| GBP  | £ | 💷 | Britaniya funti | British Pound |
| JPY  | ¥ | 🇯🇵 | Yaponiya yenasi | Japanese Yen |
| CHF  | Fr | 🇨🇭 | Shveytsariya franki | Swiss Franc |

## New Commands

### Basic Commands
- `/start` - Start the bot and set default currency
- `/rate` - Get rate for your selected currency
- `/rates` - Get rates for all supported currencies
- `/currencies` - Show currency selection menu
- `/help` - Show help with all commands
- `/status` - Show bot status and your selected currency

### Currency-Specific Commands
- `/rate_usd` - Get USD rate specifically
- `/rate_eur` - Get EUR rate specifically
- `/rate_rub` - Get RUB rate specifically
- `/rate_gbp` - Get GBP rate specifically
- `/rate_jpy` - Get JPY rate specifically
- `/rate_chf` - Get CHF rate specifically

## New Features

### 1. Currency Selection
Users can now select their preferred currency through:
- Inline keyboard buttons in `/currencies` command
- Button interface in the main menu
- Individual currency-specific commands

### 2. User Preferences
- Each user's currency preference is saved
- Preferences persist between bot restarts
- Default currency is USD for new users

### 3. Smart Notifications
- Users only receive notifications for their selected currency
- Different change thresholds for different currencies:
  - Major currencies (USD, EUR, GBP, CHF): 10 som threshold
  - Russian Ruble (RUB): 2 som threshold  
  - Japanese Yen (JPY): 1 som threshold
  - Others: 5 som threshold

### 4. Multiple Data Sources
- **CBU.uz** - Central Bank of Uzbekistan (primary source)
- **Bank.uz** - Commercial bank rates (when available)
- **Exchange Rate API** - Fallback source for calculations

### 5. Enhanced Interface
- Currency selection with emoji and names
- Multi-currency overview in single command
- Currency-specific status information
- Improved help documentation

## Usage Examples

### Setting Your Currency
1. Send `/start` to begin
2. Click "💱 Valyuta Tanlash" or use `/currencies`
3. Select your preferred currency from the buttons
4. Your selection is saved automatically

### Getting Rates
```
/rate           → Your selected currency rate
/rates          → All currency rates
/rate_eur       → Euro rate specifically
/currencies     → Currency selection menu
```

### Interface Navigation
- Main menu has currency selection button
- Currency selection includes back navigation
- All rate displays show currency-specific information

## Technical Details

### Configuration
Currency information is stored in `src/utils/config.py`:
```python
SUPPORTED_CURRENCIES = {
    'USD': {
        'name_uz': 'AQSh dollari',
        'name_en': 'US Dollar',
        'symbol': '$',
        'bank_uz_slug': 'dollar-ssha',
        'cbu_code': 'USD',
        'rate_range': (12000, 15000),
        'emoji': '💵'
    },
    # ... other currencies
}
```

### Data Storage
- `last_rates.json` - Stores current rates for all currencies
- `user_preferences.json` - Stores user currency preferences  
- `bot_users.json` - Stores subscribed users list

### Rate Sources Priority
1. **CBU.uz** - Official central bank rates (highest priority)
2. **Bank.uz** - Commercial bank rates (when available)
3. **Exchange Rate API** - Calculated rates (fallback)

## Migration from USD-only

The bot maintains backward compatibility:
- Existing users default to USD if no preference set
- Old rate commands still work
- Previous notification settings are preserved
- Legacy rate files are automatically migrated

## Adding New Currencies

To add support for a new currency:

1. Add currency info to `SUPPORTED_CURRENCIES` in `config.py`
2. Ensure the currency is available in CBU API
3. Test rate scraping with the test script
4. Update documentation

Example:
```python
'CNY': {
    'name_uz': 'Xitoy yuani',
    'name_en': 'Chinese Yuan',
    'symbol': '¥',
    'bank_uz_slug': 'xitoy-yuani',
    'cbu_code': 'CNY',
    'rate_range': (1800, 2200),
    'emoji': '🇨🇳'
}
```

## Testing

Run the test suite to verify functionality:
```bash
python3 tests/test_multi_currency.py
```

This tests:
- Currency information retrieval
- Rate scraping for multiple sources
- Message formatting for all currencies
- Interface generation

The test output shows live rates and verifies all components work correctly. 