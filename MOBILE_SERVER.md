# 📱 Running Bot on Mobile Device as Server

This guide shows how to run your USD/UZS Exchange Rate Bot directly on your phone as a server.

## 🚀 Why Run on Mobile?

- **24/7 Availability**: Your phone can be a always-on server
- **Free Hosting**: No need for paid cloud servers
- **Full Control**: Complete control over your bot
- **Learning**: Great way to learn about servers and Linux

## 📱 Android Setup (Recommended)

### Step 1: Install Termux

**⚠️ Important**: Don't use Google Play Store version!

**Best Options:**
- **F-Droid** (Recommended): https://f-droid.org/en/packages/com.termux/
- **GitHub**: https://github.com/termux/termux-app/releases

### Step 2: Initial Setup

Open Termux and run:

```bash
# Update packages
pkg update && pkg upgrade

# Install required tools
pkg install python git nano tmux curl

# Navigate to home directory
cd ~
```

### Step 3: Get Your Bot

```bash
# Clone your bot project
git clone https://github.com/your-username/kurs_bot.git
cd kurs_bot

# Run the mobile setup script
chmod +x setup_mobile.sh
./setup_mobile.sh
```

### Step 4: Configure Bot

```bash
# Edit configuration file
nano .env
```

Add your bot token:
```env
BOT_TOKEN=your_bot_token_from_botfather
CHAT_ID=your_telegram_id_optional
CHECK_INTERVAL=120
```

### Step 5: Start Your Mobile Server

```bash
# Start the bot server
./start_mobile.sh

# Check if it's running
./check_mobile.sh
```

## 🍎 iPhone Setup

### Step 1: Install iSH

Download [iSH](https://apps.apple.com/us/app/ish-shell/id1436902243) from App Store.

### Step 2: Setup

```bash
# Update packages
apk update
apk add python3 py3-pip git nano tmux

# Get your project
git clone https://github.com/your-username/kurs_bot.git
cd kurs_bot

# Install dependencies
pip3 install -r requirements.txt

# Configure and run
nano .env  # Add your bot token
python3 bot.py
```

## 🔧 Mobile Server Management

### Essential Commands

```bash
# Start bot server
./start_mobile.sh

# Check server status
./check_mobile.sh

# View live logs
tmux attach -t botserver

# Stop server
tmux kill-session -t botserver

# Restart server
tmux kill-session -t botserver
./start_mobile.sh
```

### Background Session Management

```bash
# List all sessions
tmux list-sessions

# Attach to bot session
tmux attach -t botserver

# Detach from session (Ctrl+B, then D)
# Bot continues running in background

# Kill specific session
tmux kill-session -t botserver
```

## ⚡ Performance Optimization

### Battery & Performance Tips

1. **Disable Battery Optimization**:
   - Android: Settings → Apps → Termux → Battery → Don't optimize
   - Keep Termux in recent apps

2. **Network Settings**:
   - Use Wi-Fi for stability
   - Configure mobile data if needed

3. **Storage Management**:
   ```bash
   # Check available space
   df -h
   
   # Clean up if needed
   pkg clean
   pip cache purge
   ```

### Auto-Start on Boot (Advanced)

Create a startup script:

```bash
# Install Termux:Boot addon
# Add to ~/.termux/boot/start-bot.sh

#!/data/data/com.termux/files/usr/bin/bash
cd ~/kurs_bot
./start_mobile.sh
```

## 📊 Monitoring Your Mobile Server

### Real-time Monitoring

```bash
# CPU and memory usage
top

# Network connections
netstat -tuln

# Check bot process
ps aux | grep python

# View logs in real-time
tmux attach -t botserver
```

### Health Check Script

Create `health_check.sh`:

```bash
#!/data/data/com.termux/files/usr/bin/bash

echo "🏥 Mobile Server Health Check"
echo "============================"

# Check bot session
if tmux has-session -t botserver 2>/dev/null; then
    echo "✅ Bot: RUNNING"
else
    echo "❌ Bot: DOWN - Restarting..."
    ./start_mobile.sh
fi

# System resources
echo "📊 Resources:"
echo "   Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
echo "   Storage: $(df -h . | tail -1 | awk '{print $4 " free"}')"
echo "   Uptime: $(uptime -p)"

# Network test
if ping -c 1 ******* &> /dev/null; then
    echo "🌐 Network: ONLINE"
else
    echo "❌ Network: OFFLINE"
fi
```

## 🚨 Troubleshooting

### Common Issues

**Bot Not Starting:**
```bash
# Check Python version
python3 --version

# Check dependencies
pip list | grep telegram

# View detailed logs
tmux attach -t botserver
```

**Network Issues:**
```bash
# Test internet connection
ping google.com

# Check DNS
nslookup telegram.org

# Restart network (if needed)
# Toggle airplane mode on/off
```

**Memory Issues:**
```bash
# Check memory usage
free -h

# Kill unnecessary processes
pkill -f "process_name"

# Restart Termux if needed
```

**Storage Full:**
```bash
# Check space
df -h

# Clean up
pkg clean
pip cache purge
rm -rf ~/.cache/*
```

### Recovery Commands

```bash
# Emergency restart
pkill -f python
./start_mobile.sh

# Full reset (if needed)
rm -rf ~/kurs_bot
# Re-clone and setup again
```

## 🔒 Security Considerations

### Protect Your Bot

1. **Keep Token Secret**: Never share your bot token
2. **Regular Updates**: Update packages regularly
3. **Firewall**: Termux doesn't expose ports by default
4. **Backup**: Regular backup of configuration

```bash
# Update everything
pkg update && pkg upgrade
pip install --upgrade -r requirements.txt

# Backup configuration
cp .env .env.backup
```

## 📈 Scaling Up

### Multiple Bots

Run multiple bots on same device:

```bash
# Clone for different bot
git clone ... kurs_bot_2
cd kurs_bot_2
# Configure different session name in scripts
```

### Advanced Features

```bash
# Add monitoring
pkg install htop neofetch

# Network tools
pkg install nmap netstat-nat

# File management
pkg install mc ranger
```

## 💡 Tips & Tricks

### Useful Aliases

Add to `~/.bashrc`:

```bash
alias bot-start='cd ~/kurs_bot && ./start_mobile.sh'
alias bot-stop='tmux kill-session -t botserver'
alias bot-logs='tmux attach -t botserver'
alias bot-status='cd ~/kurs_bot && ./check_mobile.sh'
```

### Quick Setup Command

One-liner for new setup:
```bash
curl -sSL https://raw.githubusercontent.com/your-repo/kurs_bot/main/setup_mobile.sh | bash
```

## 🎯 Conclusion

Your phone is now a powerful Telegram bot server! With proper setup and monitoring, it can reliably serve your bot 24/7.

**Advantages:**
- ✅ Free hosting
- ✅ Full control
- ✅ Always available
- ✅ Easy to manage

**Best Practices:**
- Keep phone charged
- Stable internet connection
- Regular monitoring
- Backup configurations

Happy mobile server hosting! 🚀📱 