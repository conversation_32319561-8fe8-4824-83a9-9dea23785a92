#!/bin/bash
# Server Deployment Script for USD/UZS Exchange Rate Bot

echo "🚀 Deploying USD/UZS Exchange Rate Bot to Server..."

# Update system
echo "📦 Updating system..."
sudo apt update && sudo apt upgrade -y

# Install Python and pip
echo "🐍 Installing Python..."
sudo apt install python3 python3-pip python3-venv git -y

# Create user for the bot (optional but recommended)
echo "👤 Creating bot user..."
sudo useradd -m -s /bin/bash botuser || echo "User already exists"

# Switch to bot user directory
sudo su - botuser << 'EOF'
# Clone or copy your bot files here
# If you're uploading files manually, skip the git clone
git clone https://your-repo-url.git kurs_bot || echo "Using uploaded files"
cd kurs_bot

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env
echo "✏️ Edit .env file with your credentials"

# Test the bot
python3 test_rate_scraper.py
EOF

# Create systemd service
echo "⚙️ Creating systemd service..."
sudo tee /etc/systemd/system/valyuta-bot.service > /dev/null << 'EOF'
[Unit]
Description=USD/UZS Exchange Rate Telegram Bot
After=network.target

[Service]
Type=simple
User=botuser
WorkingDirectory=/home/<USER>/kurs_bot
Environment=PATH=/home/<USER>/kurs_bot/venv/bin
ExecStart=/home/<USER>/kurs_bot/venv/bin/python bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable valyuta-bot.service

echo "✅ Deployment complete!"
echo ""
echo "To start the bot:"
echo "sudo systemctl start valyuta-bot"
echo ""
echo "To check status:"
echo "sudo systemctl status valyuta-bot"
echo ""
echo "To view logs:"
echo "sudo journalctl -u valyuta-bot -f" 