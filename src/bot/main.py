import asyncio
import logging
import json
import os
from datetime import datetime
from typing import Optional, Dict
import threading
import time

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, ContextTypes, CallbackQueryHandler
from telegram.constants import ParseMode

from ..core.rate_scraper import ExchangeRateScraper
from ..core.message_formatter import MessageFormatter
from ..utils.config import Config

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class ExchangeRateBot:
    def __init__(self):
        self.scraper = ExchangeRateScraper(timeout=Config.TIMEOUT)
        self.formatter = MessageFormatter()
        self.application = None
        self.current_rates = {}  # Store rates for multiple currencies
        self.previous_rates = {}  # Store previous rates for multiple currencies
        self.user_selected_currencies = {}  # Store user's selected currency for display
        self.user_currency_notifications = {}  # Store user's currency notification preferences
        self.is_running = False
        self.last_check = None
        self.rate_file = "last_rates.json"
        self.users_file = "bot_users.json"
        self.user_prefs_file = "user_preferences.json"
        self.user_notifications_file = "user_notifications.json"
        self.subscribed_users = set()
        
        # Load previous rates and preferences
        self.load_previous_rates()
        self.load_subscribed_users()
        self.load_user_preferences()
        self.load_user_notifications()
    
    def load_previous_rates(self):
        """Load the last saved rates from file"""
        try:
            if os.path.exists(self.rate_file):
                with open(self.rate_file, 'r', encoding='utf-8') as f:
                    self.previous_rates = json.load(f)
                    logger.info("Loaded previous rates from file")
        except Exception as e:
            logger.error(f"Error loading previous rates: {e}")
    
    def save_current_rates(self):
        """Save current rates to file"""
        try:
            if self.current_rates:
                with open(self.rate_file, 'w', encoding='utf-8') as f:
                    json.dump(self.current_rates, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving current rates: {e}")
    
    def load_user_preferences(self):
        """Load user preferences from file"""
        try:
            if os.path.exists(self.user_prefs_file):
                with open(self.user_prefs_file, 'r', encoding='utf-8') as f:
                    self.user_selected_currencies = json.load(f)
                    # Convert string keys to int for user IDs
                    self.user_selected_currencies = {
                        int(k): v for k, v in self.user_selected_currencies.items()
                    }
                    logger.info(f"Loaded user preferences for {len(self.user_selected_currencies)} users")
        except Exception as e:
            logger.error(f"Error loading user preferences: {e}")
    
    def save_user_preferences(self):
        """Save user preferences to file"""
        try:
            # Convert int keys to string for JSON serialization
            prefs_to_save = {str(k): v for k, v in self.user_selected_currencies.items()}
            with open(self.user_prefs_file, 'w', encoding='utf-8') as f:
                json.dump(prefs_to_save, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving user preferences: {e}")
    
    def set_user_currency(self, user_id: int, currency_code: str):
        """Set user's preferred currency"""
        if Config.get_currency_info(currency_code):
            self.user_selected_currencies[user_id] = currency_code
            self.save_user_preferences()
            logger.info(f"Set currency {currency_code} for user {user_id}")
            return True
        return False
    
    def get_user_currency(self, user_id: int) -> str:
        """Get user's preferred currency"""
        return self.user_selected_currencies.get(user_id, Config.DEFAULT_CURRENCY)
    
    def load_user_notifications(self):
        """Load user currency notification preferences from file"""
        try:
            if os.path.exists(self.user_notifications_file):
                with open(self.user_notifications_file, 'r', encoding='utf-8') as f:
                    self.user_currency_notifications = json.load(f)
                    # Convert string keys to int for user IDs
                    self.user_currency_notifications = {
                        int(k): v for k, v in self.user_currency_notifications.items()
                    }
                    logger.info(f"Loaded currency notifications for {len(self.user_currency_notifications)} users")
        except Exception as e:
            logger.error(f"Error loading user notifications: {e}")
    
    def save_user_notifications(self):
        """Save user currency notification preferences to file"""
        try:
            # Convert int keys to string for JSON serialization
            notifications_to_save = {str(k): v for k, v in self.user_currency_notifications.items()}
            with open(self.user_notifications_file, 'w', encoding='utf-8') as f:
                json.dump(notifications_to_save, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving user notifications: {e}")
    
    def toggle_currency_notification(self, user_id: int, currency_code: str) -> bool:
        """Toggle notification for a specific currency for a user"""
        if user_id not in self.user_currency_notifications:
            self.user_currency_notifications[user_id] = {}
        
        current_state = self.user_currency_notifications[user_id].get(currency_code, False)
        self.user_currency_notifications[user_id][currency_code] = not current_state
        self.save_user_notifications()
        
        logger.info(f"User {user_id} {'enabled' if not current_state else 'disabled'} notifications for {currency_code}")
        return not current_state
    
    def is_currency_notification_enabled(self, user_id: int, currency_code: str) -> bool:
        """Check if user has notifications enabled for a specific currency"""
        if user_id not in self.user_currency_notifications:
            # Default: enable notifications for user's selected currency
            user_currency = self.get_user_currency(user_id)
            return currency_code == user_currency
        
        return self.user_currency_notifications[user_id].get(currency_code, False)
    
    def get_user_notification_currencies(self, user_id: int) -> list:
        """Get list of currencies user has notifications enabled for"""
        if user_id not in self.user_currency_notifications:
            # Default: user's selected currency
            return [self.get_user_currency(user_id)]
        
        enabled_currencies = [
            currency for currency, enabled in self.user_currency_notifications[user_id].items() 
            if enabled
        ]
        
        # If no currencies enabled, default to user's selected currency
        if not enabled_currencies:
            return [self.get_user_currency(user_id)]
        
        return enabled_currencies
    
    def load_subscribed_users(self):
        """Load subscribed users from file"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    user_list = json.load(f)
                    self.subscribed_users = set(user_list)
                    logger.info(f"Loaded {len(self.subscribed_users)} subscribed users")
        except Exception as e:
            logger.error(f"Error loading subscribed users: {e}")
    
    def save_subscribed_users(self):
        """Save subscribed users to file"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.subscribed_users), f, indent=2)
        except Exception as e:
            logger.error(f"Error saving subscribed users: {e}")
    
    def subscribe_user(self, user_id: int):
        """Subscribe a user to rate notifications"""
        self.subscribed_users.add(user_id)
        self.save_subscribed_users()
        logger.info(f"User {user_id} subscribed to notifications")
    
    def unsubscribe_user(self, user_id: int):
        """Unsubscribe a user from rate notifications"""
        self.subscribed_users.discard(user_id)
        self.save_subscribed_users()
        logger.info(f"User {user_id} unsubscribed from notifications")
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        user_id = update.effective_user.id
        
        # Subscribe user to notifications and set default currency
        self.subscribe_user(user_id)
        if user_id not in self.user_selected_currencies:
            self.set_user_currency(user_id, Config.DEFAULT_CURRENCY)
        
        user_currency = self.get_user_currency(user_id)
        currency_info = Config.get_currency_info(user_currency)
        
        welcome_message = (
            "👋 Salom! Men valyuta kurslari botiman.\n\n"
            "🔔 Siz avtomatik xabarlar ro'yxatiga qo'shildingiz!\n"
            "💡 Kurs o'zgarganda sizga xabar beraman.\n\n"
            f"💱 Sizning tanlangan valyutangiz: {currency_info['emoji']} {currency_info['name_uz']}\n\n"
            "Quyidagi tugmalardan birini tanlang:"
        )
        
        # Create inline keyboard
        keyboard = [
            [
                InlineKeyboardButton("💰 Joriy Kurs", callback_data="get_rate"),
                InlineKeyboardButton("💱 Valyuta Tanlash", callback_data="select_currency")
            ],
            [
                InlineKeyboardButton("📊 Barcha Kurslar", callback_data="get_all_rates"),
                InlineKeyboardButton("📈 Bot Holati", callback_data="get_status")
            ],
            [
                InlineKeyboardButton("🔔 Xabarlar", callback_data="toggle_notifications"),
                InlineKeyboardButton("❓ Yordam", callback_data="get_help")
            ],
            [
                InlineKeyboardButton("🔄 Yangilash", callback_data="refresh")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            welcome_message, 
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def rate_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /rate command - shows user's selected currency"""
        try:
            user_id = update.effective_user.id
            user_currency = self.get_user_currency(user_id)
            
            # Get current rate for user's selected currency
            current_rate = self.scraper.get_best_rate(user_currency)
            
            if current_rate:
                message = self.formatter.format_rate_request(current_rate)
                await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)
            else:
                currency_info = Config.get_currency_info(user_currency)
                error_msg = self.formatter.format_error_message(
                    f"Hozirda {currency_info['name_uz']} kursini olishda muammo. Keyinroq qayta urining."
                )
                await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
                
        except Exception as e:
            logger.error(f"Error in rate_command: {e}")
            error_msg = self.formatter.format_error_message(
                "Buyruqni bajarishda xatolik yuz berdi."
            )
            await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
    
    async def rates_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /rates command - shows all currency rates, including forecast if available"""
        try:
            # Get rates for all supported currencies
            currency_codes = Config.get_currency_list()
            rates_data = self.scraper.get_multiple_currencies(currency_codes)
            forecast_data = self.scraper.get_forecast_rates()
            if rates_data:
                message = self.formatter.format_multiple_rates(rates_data, forecast_data)
                await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)
            else:
                error_msg = self.formatter.format_error_message(
                    "Hozirda valyuta kurslarini olishda muammo. Keyinroq qayta urining."
                )
                await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
        except Exception as e:
            logger.error(f"Error in rates_command: {e}")
            try:
                error_msg = self.formatter.format_error_message(
                    "Hozirda valyuta kurslarini ko'rsatishda muammo. Keyinroq qayta urining."
                )
                await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
            except Exception as e2:
                logger.error(f"Error sending error message: {e2}")
                # Send simple text without markdown
                await update.message.reply_text(
                    "⚠️ Hozirda valyuta kurslarini ko'rsatishda muammo. Keyinroq qayta urining."
                )
    
    async def currencies_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /currencies command - shows currency selection"""
        try:
            message = self.formatter.format_currency_selection()
            
            # Create inline keyboard for currency selection
            keyboard = []
            currencies = Config.get_currency_list()
            
            # Create rows with 2 currencies each
            for i in range(0, len(currencies), 2):
                row = []
                for j in range(i, min(i + 2, len(currencies))):
                    currency_code = currencies[j]
                    currency_info = Config.get_currency_info(currency_code)
                    button_text = f"{currency_info['emoji']} {currency_code}"
                    row.append(InlineKeyboardButton(
                        button_text, 
                        callback_data=f"select_{currency_code}"
                    ))
                keyboard.append(row)
            
            # Add back button
            keyboard.append([InlineKeyboardButton("🔙 Orqaga", callback_data="back_to_main")])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                message, 
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )
                
        except Exception as e:
            logger.error(f"Error in currencies_command: {e}")
            error_msg = self.formatter.format_error_message(
                "Buyruqni bajarishda xatolik yuz berdi."
            )
            await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
    
    async def notifications_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /notifications command - shows currency notification settings"""
        try:
            user_id = update.effective_user.id
            message = self.formatter.format_notification_settings(user_id, self.user_currency_notifications.get(user_id, {}))
            
            # Create inline keyboard for notification management
            keyboard = []
            currencies = Config.get_currency_list()
            
            # Create rows with currency notification toggles
            for currency_code in currencies:
                currency_info = Config.get_currency_info(currency_code)
                is_enabled = self.is_currency_notification_enabled(user_id, currency_code)
                status_emoji = "🔔" if is_enabled else "🔕"
                button_text = f"{status_emoji} {currency_info['emoji']} {currency_code}"
                
                keyboard.append([InlineKeyboardButton(
                    button_text, 
                    callback_data=f"notify_{currency_code}"
                )])
            
            # Add control buttons
            keyboard.append([
                InlineKeyboardButton("✅ Barcha", callback_data="notify_all_on"),
                InlineKeyboardButton("❌ Hech qaysi", callback_data="notify_all_off")
            ])
            keyboard.append([InlineKeyboardButton("🔙 Orqaga", callback_data="back_to_main")])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                message, 
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )
                
        except Exception as e:
            logger.error(f"Error in notifications_command: {e}")
            error_msg = self.formatter.format_error_message(
                "Xabar sozlamalarini ko'rsatishda xatolik yuz berdi."
            )
            await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
    
    async def specific_rate_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /rate_XXX commands for specific currencies"""
        try:
            command = update.message.text.strip()
            # Extract currency code from command like /rate_USD
            if command.startswith('/rate_') and len(command) > 6:
                currency_code = command[6:].upper()
                
                # Check if currency is supported
                if not Config.get_currency_info(currency_code):
                    error_msg = self.formatter.format_error_message(
                        f"Valyuta {currency_code} qo'llab-quvvatlanmaydi. /currencies ni ko'ring."
                    )
                    await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
                    return
                
                # Get rate for specific currency
                current_rate = self.scraper.get_best_rate(currency_code)
                
                if current_rate:
                    message = self.formatter.format_rate_request(current_rate)
                    await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)
                else:
                    currency_info = Config.get_currency_info(currency_code)
                    error_msg = self.formatter.format_error_message(
                        f"Hozirda {currency_info['name_uz']} kursini olishda muammo. Keyinroq qayta urining."
                    )
                    await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
            else:
                error_msg = self.formatter.format_error_message(
                    "Noto'g'ri buyruq formati. Masalan: /rate_USD"
                )
                await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
                
        except Exception as e:
            logger.error(f"Error in specific_rate_command: {e}")
            error_msg = self.formatter.format_error_message(
                "Buyruqni bajarishda xatolik yuz berdi."
            )
            await update.message.reply_text(error_msg, parse_mode=ParseMode.MARKDOWN)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = self.formatter.format_help_message()
        await update.message.reply_text(help_message, parse_mode=ParseMode.MARKDOWN)
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        user_id = update.effective_user.id
        user_currency = self.get_user_currency(user_id)
        current_rate = self.current_rates.get(user_currency)
        
        status_message = self.formatter.format_status_message(
            is_running=self.is_running,
            last_check=self.last_check,
            last_rate=current_rate,
            selected_currency=user_currency
        )
        await update.message.reply_text(status_message, parse_mode=ParseMode.MARKDOWN)
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline button callbacks"""
        query = update.callback_query
        user_id = update.effective_user.id
        await query.answer()
        
        if query.data == "get_rate":
            # Get current rate for user's selected currency
            try:
                user_currency = self.get_user_currency(user_id)
                current_rate = self.scraper.get_best_rate(user_currency)
                if current_rate:
                    message = self.formatter.format_rate_request(current_rate)
                else:
                    currency_info = Config.get_currency_info(user_currency)
                    message = self.formatter.format_error_message(
                        f"Hozirda {currency_info['name_uz']} kursini olishda muammo. Keyinroq qayta urining."
                    )
            except Exception as e:
                logger.error(f"Error in button callback rate: {e}")
                message = self.formatter.format_error_message("Buyruqni bajarishda xatolik yuz berdi.")
                
        elif query.data == "get_all_rates":
            # Get rates for all currencies
            try:
                currency_codes = Config.get_currency_list()
                rates_data = self.scraper.get_multiple_currencies(currency_codes)
                if rates_data:
                    message = self.formatter.format_multiple_rates(rates_data)
                else:
                    message = self.formatter.format_error_message(
                        "Hozirda valyuta kurslarini olishda muammo. Keyinroq qayta urining."
                    )
            except Exception as e:
                logger.error(f"Error in button callback all rates: {e}")
                message = self.formatter.format_error_message("Buyruqni bajarishda xatolik yuz berdi.")
                
        elif query.data == "select_currency":
            # Show currency selection
            try:
                message = self.formatter.format_currency_selection()
                
                # Create inline keyboard for currency selection
                keyboard = []
                currencies = Config.get_currency_list()
                
                # Create rows with 2 currencies each
                for i in range(0, len(currencies), 2):
                    row = []
                    for j in range(i, min(i + 2, len(currencies))):
                        currency_code = currencies[j]
                        currency_info = Config.get_currency_info(currency_code)
                        button_text = f"{currency_info['emoji']} {currency_code}"
                        row.append(InlineKeyboardButton(
                            button_text, 
                            callback_data=f"select_{currency_code}"
                        ))
                    keyboard.append(row)
                
                # Add back button
                keyboard.append([InlineKeyboardButton("🔙 Orqaga", callback_data="back_to_main")])
                
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await query.edit_message_text(
                    message, 
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return
                
            except Exception as e:
                logger.error(f"Error in button callback currency selection: {e}")
                try:
                    message = self.formatter.format_error_message("Valyuta tanlashda muammo. Qayta urining.")
                except:
                    message = "⚠️ Valyuta tanlashda muammo. Qayta urining."
                
        elif query.data.startswith("select_"):
            # Handle currency selection
            try:
                currency_code = query.data[7:]  # Remove "select_" prefix
                
                if Config.get_currency_info(currency_code):
                    # Set user's currency preference
                    self.set_user_currency(user_id, currency_code)
                    currency_info = Config.get_currency_info(currency_code)
                    
                    message = (
                        f"✅ **Valyuta tanlandi!**\n\n"
                        f"💱 Sizning yangi valyutangiz: {currency_info['emoji']} {currency_info['name_uz']}\n\n"
                        f"Endi /rate buyrug'i {currency_info['name_uz']} kursini ko'rsatadi."
                    )
                    
                    # Create back to main keyboard
                    keyboard = [[InlineKeyboardButton("🔙 Bosh menyuga", callback_data="back_to_main")]]
                    reply_markup = InlineKeyboardMarkup(keyboard)
                    
                    await query.edit_message_text(
                        message, 
                        parse_mode=ParseMode.MARKDOWN,
                        reply_markup=reply_markup
                    )
                    return
                else:
                    message = self.formatter.format_error_message(
                        f"Valyuta {currency_code} qo'llab-quvvatlanmaydi."
                    )
                    
            except Exception as e:
                logger.error(f"Error in currency selection: {e}")
                message = self.formatter.format_error_message("Valyuta tanlashda xatolik yuz berdi.")
                
        elif query.data == "back_to_main":
            # Back to main menu
            try:
                user_currency = self.get_user_currency(user_id)
                currency_info = Config.get_currency_info(user_currency)
                
                welcome_message = (
                    "👋 Bosh menyu\n\n"
                    f"💱 Sizning tanlangan valyutangiz: {currency_info['emoji']} {currency_info['name_uz']}\n\n"
                    "Quyidagi tugmalardan birini tanlang:"
                )
                
                # Create main keyboard
                keyboard = [
                    [
                        InlineKeyboardButton("💰 Joriy Kurs", callback_data="get_rate"),
                        InlineKeyboardButton("💱 Valyuta Tanlash", callback_data="select_currency")
                    ],
                    [
                        InlineKeyboardButton("📊 Barcha Kurslar", callback_data="get_all_rates"),
                        InlineKeyboardButton("📈 Bot Holati", callback_data="get_status")
                    ],
                    [
                        InlineKeyboardButton("🔔 Xabarlar", callback_data="toggle_notifications"),
                        InlineKeyboardButton("❓ Yordam", callback_data="get_help")
                    ],
                    [
                        InlineKeyboardButton("🔄 Yangilash", callback_data="refresh")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await query.edit_message_text(
                    welcome_message, 
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return
                
            except Exception as e:
                logger.error(f"Error in back to main: {e}")
                message = self.formatter.format_error_message("Buyruqni bajarishda xatolik yuz berdi.")
                
        elif query.data == "get_status":
            user_currency = self.get_user_currency(user_id)
            current_rate = self.current_rates.get(user_currency)
            message = self.formatter.format_status_message(
                is_running=self.is_running,
                last_check=self.last_check,
                last_rate=current_rate,
                selected_currency=user_currency
            )
            # Add subscription count
            message += f"\n📊 **Obunachi foydalanuvchilar:** {len(self.subscribed_users)}"
            
        elif query.data == "get_help":
            message = self.formatter.format_help_message()
            
        elif query.data == "manage_notifications":
            # Show notification management interface
            try:
                message = self.formatter.format_notification_settings(user_id, self.user_currency_notifications.get(user_id, {}))
                
                # Create inline keyboard for notification management
                keyboard = []
                currencies = Config.get_currency_list()
                
                # Create rows with currency notification toggles
                for currency_code in currencies:
                    currency_info = Config.get_currency_info(currency_code)
                    is_enabled = self.is_currency_notification_enabled(user_id, currency_code)
                    status_emoji = "🔔" if is_enabled else "🔕"
                    button_text = f"{status_emoji} {currency_info['emoji']} {currency_code}"
                    
                    keyboard.append([InlineKeyboardButton(
                        button_text, 
                        callback_data=f"notify_{currency_code}"
                    )])
                
                # Add control buttons
                keyboard.append([
                    InlineKeyboardButton("✅ Barcha", callback_data="notify_all_on"),
                    InlineKeyboardButton("❌ Hech qaysi", callback_data="notify_all_off")
                ])
                keyboard.append([InlineKeyboardButton("🔙 Orqaga", callback_data="back_to_main")])
                
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await query.edit_message_text(
                    message, 
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return
                
            except Exception as e:
                logger.error(f"Error in notification management: {e}")
                message = self.formatter.format_error_message("Xabar sozlamalarini ko'rsatishda xatolik yuz berdi.")
                
        elif query.data.startswith("notify_"):
            # Handle currency notification toggle
            try:
                if query.data == "notify_all_on":
                    # Enable all currencies
                    if user_id not in self.user_currency_notifications:
                        self.user_currency_notifications[user_id] = {}
                    
                    for currency_code in Config.get_currency_list():
                        self.user_currency_notifications[user_id][currency_code] = True
                    
                    self.save_user_notifications()
                    
                elif query.data == "notify_all_off":
                    # Disable all currencies
                    if user_id not in self.user_currency_notifications:
                        self.user_currency_notifications[user_id] = {}
                    
                    for currency_code in Config.get_currency_list():
                        self.user_currency_notifications[user_id][currency_code] = False
                    
                    self.save_user_notifications()
                    
                else:
                    # Toggle specific currency
                    currency_code = query.data[7:]  # Remove "notify_" prefix
                    
                    if Config.get_currency_info(currency_code):
                        self.toggle_currency_notification(user_id, currency_code)
                    else:
                        message = self.formatter.format_error_message("Noma'lum valyuta.")
                
                # Update the interface
                updated_message = self.formatter.format_notification_settings(user_id, self.user_currency_notifications.get(user_id, {}))
                
                # Create updated keyboard
                keyboard = []
                currencies = Config.get_currency_list()
                
                for currency_code in currencies:
                    currency_info = Config.get_currency_info(currency_code)
                    is_enabled = self.is_currency_notification_enabled(user_id, currency_code)
                    status_emoji = "🔔" if is_enabled else "🔕"
                    button_text = f"{status_emoji} {currency_info['emoji']} {currency_code}"
                    
                    keyboard.append([InlineKeyboardButton(
                        button_text, 
                        callback_data=f"notify_{currency_code}"
                    )])
                
                keyboard.append([
                    InlineKeyboardButton("✅ Barcha", callback_data="notify_all_on"),
                    InlineKeyboardButton("❌ Hech qaysi", callback_data="notify_all_off")
                ])
                keyboard.append([InlineKeyboardButton("🔙 Orqaga", callback_data="back_to_main")])
                
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await query.edit_message_text(
                    updated_message, 
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return
                
            except Exception as e:
                logger.error(f"Error in currency notification toggle: {e}")
                message = self.formatter.format_error_message("Xabar sozlamalarini o'zgartirishda xatolik yuz berdi.")
                
        elif query.data == "toggle_notifications":
            # Legacy support - redirect to new notification management
            try:
                message = self.formatter.format_notification_settings(user_id, self.user_currency_notifications.get(user_id, {}))
                
                keyboard = []
                currencies = Config.get_currency_list()
                
                for currency_code in currencies:
                    currency_info = Config.get_currency_info(currency_code)
                    is_enabled = self.is_currency_notification_enabled(user_id, currency_code)
                    status_emoji = "🔔" if is_enabled else "🔕"
                    button_text = f"{status_emoji} {currency_info['emoji']} {currency_code}"
                    
                    keyboard.append([InlineKeyboardButton(
                        button_text, 
                        callback_data=f"notify_{currency_code}"
                    )])
                
                keyboard.append([
                    InlineKeyboardButton("✅ Barcha", callback_data="notify_all_on"),
                    InlineKeyboardButton("❌ Hech qaysi", callback_data="notify_all_off")
                ])
                keyboard.append([InlineKeyboardButton("🔙 Orqaga", callback_data="back_to_main")])
                
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await query.edit_message_text(
                    message, 
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return
                
            except Exception as e:
                logger.error(f"Error in legacy notification toggle: {e}")
                message = self.formatter.format_error_message("Xabar sozlamalarini ko'rsatishda xatolik yuz berdi.")
            
        elif query.data == "refresh":
            # Create the same keyboard as in start command
            keyboard = [
                [
                    InlineKeyboardButton("💰 Joriy Kurs", callback_data="get_rate"),
                    InlineKeyboardButton("📊 Bot Holati", callback_data="get_status")
                ],
                [
                    InlineKeyboardButton("🔔 Xabarlar", callback_data="toggle_notifications"),
                    InlineKeyboardButton("❓ Yordam", callback_data="get_help")
                ],
                [
                    InlineKeyboardButton("🔄 Yangilash", callback_data="refresh")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                "🔄 Interfeys yangilandi! Tugmalardan birini tanlang:",
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )
            return
        
        # For other actions, add back navigation buttons
        keyboard = [
            [
                InlineKeyboardButton("💰 Joriy Kurs", callback_data="get_rate"),
                InlineKeyboardButton("📊 Bot Holati", callback_data="get_status")
            ],
            [
                InlineKeyboardButton("🔔 Xabarlar", callback_data="toggle_notifications"),
                InlineKeyboardButton("❓ Yordam", callback_data="get_help")
            ],
            [
                InlineKeyboardButton("🔄 Yangilash", callback_data="refresh")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def send_currency_rate_update(self, currency_code: str, current_rate: Dict, previous_rate: Optional[Dict] = None):
        """Send rate update for a specific currency to users who have selected it"""
        if not self.subscribed_users and not Config.CHAT_ID:
            logger.warning("No users subscribed and no CHAT_ID configured, skipping message send")
            return
            
        message = self.formatter.format_rate_update(current_rate, previous_rate)
        sent_count = 0
        failed_count = 0
        
        # Send to users who have notifications enabled for this currency
        for user_id in list(self.subscribed_users):
            if self.is_currency_notification_enabled(user_id, currency_code):
                try:
                    await self.application.bot.send_message(
                        chat_id=user_id,
                        text=message,
                        parse_mode=ParseMode.MARKDOWN
                    )
                    sent_count += 1
                except Exception as e:
                    logger.error(f"Error sending rate update to user {user_id}: {e}")
                    failed_count += 1
                    # If user blocked the bot, remove them from subscribers
                    if "bot was blocked by the user" in str(e).lower():
                        self.unsubscribe_user(user_id)
                        logger.info(f"Removed user {user_id} from subscribers (blocked bot)")
        
        # Also send to configured CHAT_ID if it exists (for channel/group notifications)
        if Config.CHAT_ID:
            try:
                await self.application.bot.send_message(
                    chat_id=Config.CHAT_ID,
                    text=message,
                    parse_mode=ParseMode.MARKDOWN
                )
                sent_count += 1
                logger.info(f"Rate update for {currency_code} sent to configured CHAT_ID")
            except Exception as e:
                logger.error(f"Error sending rate update to CHAT_ID: {e}")
                failed_count += 1
        
        logger.info(f"{currency_code} rate update completed: {sent_count} sent, {failed_count} failed")
    
    async def send_rate_update(self, current_rate: Dict, previous_rate: Optional[Dict] = None):
        """Legacy method for backward compatibility"""
        currency_code = current_rate.get('currency', 'USD')
        await self.send_currency_rate_update(currency_code, current_rate, previous_rate)
    
    def check_rate_changes(self):
        """Check for rate changes and send updates if needed"""
        try:
            logger.info("Checking for rate changes...")
            self.last_check = datetime.now().isoformat()
            
            # Get current rates for all currencies
            currency_codes = Config.get_currency_list()
            new_rates = self.scraper.get_multiple_currencies(currency_codes)
            
            if not new_rates:
                logger.warning("Could not fetch current rates")
                return
            
            # Check each currency for changes
            for currency_code, new_rate in new_rates.items():
                if not new_rate:
                    continue
                    
                # Get previous rate for this currency
                previous_rate = self.previous_rates.get(currency_code)
                current_rate = self.current_rates.get(currency_code)
                
                rate_changed = False
                
                if current_rate is None:
                    # First time getting rate for this currency
                    rate_changed = True
                    logger.info(f"First rate fetch for {currency_code}")
                else:
                    # Check if rate changed significantly
                    current_central_rate = current_rate.get('central_bank_rate', 0)
                    new_central_rate = new_rate.get('central_bank_rate', 0)
                    
                    # Get currency-specific change threshold
                    currency_info = Config.get_currency_info(currency_code)
                    if currency_info:
                        # Base threshold on currency type
                        if currency_code in ['USD', 'EUR', 'GBP', 'CHF']:
                            threshold = 10  # 10 som for major currencies
                        elif currency_code == 'RUB':
                            threshold = 2   # 2 som for RUB
                        elif currency_code == 'JPY':
                            threshold = 1   # 1 som for JPY
                        else:
                            threshold = 5   # 5 som for others
                        
                        if abs(new_central_rate - current_central_rate) >= threshold:
                            rate_changed = True
                            logger.info(f"{currency_code} rate changed: {current_central_rate} -> {new_central_rate}")
                
                # Update current rate for this currency
                self.previous_rates[currency_code] = current_rate
                self.current_rates[currency_code] = new_rate
                
                # Send update if rate changed
                if rate_changed and self.application:
                    try:
                        # Create task in the main event loop
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            asyncio.create_task(
                                self.send_currency_rate_update(currency_code, new_rate, current_rate)
                            )
                        else:
                            # If no loop is running, schedule for later
                            logger.warning(f"No event loop running, skipping notification for {currency_code}")
                    except RuntimeError:
                        logger.warning(f"Could not schedule notification for {currency_code} - no event loop")
            
            # Save updated rates
            self.save_current_rates()
            
        except Exception as e:
            logger.error(f"Error checking rate changes: {e}")
    
    def rate_monitoring_loop(self):
        """Background loop for monitoring rates"""
        logger.info("Starting rate monitoring loop...")
        
        while self.is_running:
            try:
                self.check_rate_changes()
                time.sleep(Config.CHECK_INTERVAL)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    async def start_monitoring(self):
        """Start the rate monitoring in background"""
        if self.is_running:
            logger.warning("Monitoring already running")
            return
        
        self.is_running = True
        
        # Start monitoring in a separate thread
        monitoring_thread = threading.Thread(
            target=self.rate_monitoring_loop,
            daemon=True
        )
        monitoring_thread.start()
        
        logger.info("Rate monitoring started")
        
        # Send startup message
        if Config.CHAT_ID:
            startup_message = self.formatter.format_startup_message()
            await self.application.bot.send_message(
                chat_id=Config.CHAT_ID,
                text=startup_message,
                parse_mode=ParseMode.MARKDOWN
            )
    
    def stop_monitoring(self):
        """Stop the rate monitoring"""
        self.is_running = False
        logger.info("Rate monitoring stopped")
    
    def setup_handlers(self):
        """Setup command handlers for the bot"""
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("rate", self.rate_command))
        self.application.add_handler(CommandHandler("rates", self.rates_command))
        self.application.add_handler(CommandHandler("currencies", self.currencies_command))
        self.application.add_handler(CommandHandler("notifications", self.notifications_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        
        # Add handlers for specific currency rate commands
        for currency_code in Config.get_currency_list():
            command_name = f"rate_{currency_code.lower()}"
            self.application.add_handler(CommandHandler(command_name, self.specific_rate_command))
        
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
    
    async def post_init(self, application):
        """Post initialization callback"""
        logger.info("Bot initialized, starting monitoring...")
        await self.start_monitoring()
    
    def run(self):
        """Run the bot"""
        Config.validate()
        
        # Create application
        self.application = Application.builder().token(Config.BOT_TOKEN).post_init(self.post_init).build()
        
        # Setup handlers
        self.setup_handlers()
        
        logger.info("Starting Multi-Currency Exchange Rate Bot...")
        
        try:
            # Run the bot
            self.application.run_polling(allowed_updates=Update.ALL_TYPES)
        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        except Exception as e:
            logger.error(f"Error running bot: {e}")
        finally:
            self.stop_monitoring()

def main():
    """Main function"""
    bot = ExchangeRateBot()
    bot.run()

if __name__ == "__main__":
    main() 