import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for the Exchange Rate Bot"""
    
    # Bot configuration
    BOT_TOKEN = os.getenv('BOT_TOKEN', '')
    CHAT_ID = os.getenv('CHAT_ID', '')
    CHECK_INTERVAL = int(os.getenv('CHECK_INTERVAL', 120))  # seconds
    
    # Exchange rate sources
    BANK_UZ_URL = "https://bank.uz/uz/currency/"
    CBU_API_URL = "https://cbu.uz/uz/arkhiv-kursov-valyut/json/"
    EXCHANGE_RATE_API_URL = "https://api.exchangerate-api.com/v4/latest/"
    
    # Supported currencies configuration
    SUPPORTED_CURRENCIES = {
        'USD': {
            'name_uz': 'AQSh dollari',
            'name_en': 'US Dollar',
            'symbol': '$',
            'bank_uz_slug': None,  # Not available on bank.uz, use CBU only
            'cbu_code': 'USD',
            'rate_range': (12000, 15000),  # Reasonable rate range for UZS
            'emoji': '💵'
        },
        'EUR': {
            'name_uz': 'Yevro',
            'name_en': 'Euro',
            'symbol': '€',
            'bank_uz_slug': None,  # Not available on bank.uz, use CBU only
            'cbu_code': 'EUR',
            'rate_range': (13000, 17000),
            'emoji': '💶'
        },
        'RUB': {
            'name_uz': 'Rossiya rubli',
            'name_en': 'Russian Ruble',
            'symbol': '₽',
            'bank_uz_slug': None,  # Not available on bank.uz, use CBU only
            'cbu_code': 'RUB',
            'rate_range': (130, 170),
            'emoji': '🇷🇺'
        },
        'GBP': {
            'name_uz': 'Britaniya funti',
            'name_en': 'British Pound',
            'symbol': '£',
            'bank_uz_slug': None,  # Not available on bank.uz, use CBU only
            'cbu_code': 'GBP',
            'rate_range': (15000, 20000),
            'emoji': '💷'
        },
        'JPY': {
            'name_uz': 'Yaponiya yenasi',
            'name_en': 'Japanese Yen',
            'symbol': '¥',
            'bank_uz_slug': None,  # Not available on bank.uz, use CBU only
            'cbu_code': 'JPY',
            'rate_range': (80, 120),
            'emoji': '🇯🇵'
        },
        'CHF': {
            'name_uz': 'Shveytsariya franki',
            'name_en': 'Swiss Franc',
            'symbol': 'Fr',
            'bank_uz_slug': None,  # Not available on bank.uz, use CBU only
            'cbu_code': 'CHF',
            'rate_range': (13500, 17500),
            'emoji': '🇨🇭'
        }
    }
    
    # Default currency (backward compatibility)
    DEFAULT_CURRENCY = 'USD'
    
    # Bot settings
    MAX_RETRIES = 3
    TIMEOUT = 10
    
    @classmethod
    def validate(cls):
        """Validate that required configuration is present"""
        if not cls.BOT_TOKEN:
            raise ValueError("BOT_TOKEN is required")
        if not cls.CHAT_ID:
            raise ValueError("CHAT_ID is required")
        
        return True
    
    @classmethod
    def get_currency_info(cls, currency_code: str):
        """Get currency information by code"""
        return cls.SUPPORTED_CURRENCIES.get(currency_code.upper())
    
    @classmethod
    def get_currency_list(cls):
        """Get list of supported currency codes"""
        return list(cls.SUPPORTED_CURRENCIES.keys()) 