import requests
import json
from bs4 import BeautifulSoup
from typing import Dict, Optional, List
import logging
from datetime import datetime, timedelta
import re
from ..utils.config import Config

logger = logging.getLogger(__name__)

class ExchangeRateScraper:
    def __init__(self, timeout: int = 10):
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def get_bank_uz_rate(self, currency_code: str = 'USD') -> Optional[Dict]:
        """Scrape currency rate from bank.uz"""
        try:
            currency_info = Config.get_currency_info(currency_code)
            if not currency_info:
                logger.error(f"Unsupported currency: {currency_code}")
                return None
            
            # Skip bank.uz if no slug is configured
            if not currency_info.get('bank_uz_slug'):
                logger.debug(f"No bank.uz slug configured for {currency_code}, skipping bank.uz scraping")
                return None
            
            url = f"{Config.BANK_UZ_URL}{currency_info['bank_uz_slug']}"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find the central bank rate
            mb_rate_elem = soup.find('div', string=lambda text: text and 'MB UZ kursi' in text)
            if mb_rate_elem:
                rate_container = mb_rate_elem.find_parent()
                rate_text = rate_container.get_text()
                
                # Extract rate using regex
                rate_match = re.search(r'(\d+[\s,]?\d*)\s*so\'m', rate_text)
                if rate_match:
                    rate_str = rate_match.group(1).replace(',', '').replace(' ', '')
                    central_bank_rate = float(rate_str)
                    
                    # Get bank rates
                    buy_rates = []
                    sell_rates = []
                    
                    # Find buy/sell rates table
                    rate_tables = soup.find_all('div', class_=lambda x: x and 'rate' in str(x).lower())
                    
                    for table in rate_tables:
                        rates = table.find_all(string=re.compile(r'\d+\s*so\'m'))
                        for rate in rates:
                            rate_match = re.search(r'(\d+[\s,]?\d*)\s*so\'m', rate)
                            if rate_match:
                                rate_value = float(rate_match.group(1).replace(',', '').replace(' ', ''))
                                if 'sotib' in table.get_text().lower() or 'buy' in table.get_text().lower():
                                    buy_rates.append(rate_value)
                                elif 'sotish' in table.get_text().lower() or 'sell' in table.get_text().lower():
                                    sell_rates.append(rate_value)
                    
                    return {
                        'source': 'bank.uz',
                        'currency': currency_code,
                        'currency_info': currency_info,
                        'central_bank_rate': central_bank_rate,
                        'buy_rate_min': min(buy_rates) if buy_rates else None,
                        'buy_rate_max': max(buy_rates) if buy_rates else None,
                        'sell_rate_min': min(sell_rates) if sell_rates else None,
                        'sell_rate_max': max(sell_rates) if sell_rates else None,
                        'timestamp': datetime.now().isoformat()
                    }
            
            # Fallback: try to find any currency rate
            currency_elements = soup.find_all(string=re.compile(r'\d+[\s,]?\d*\s*so\'m'))
            if currency_elements:
                rate_range = currency_info['rate_range']
                for elem in currency_elements:
                    rate_match = re.search(r'(\d+[\s,]?\d*)\s*so\'m', elem)
                    if rate_match:
                        rate_value = float(rate_match.group(1).replace(',', '').replace(' ', ''))
                        if rate_range[0] <= rate_value <= rate_range[1]:  # Reasonable rate range
                            return {
                                'source': 'bank.uz',
                                'currency': currency_code,
                                'currency_info': currency_info,
                                'central_bank_rate': rate_value,
                                'timestamp': datetime.now().isoformat()
                            }
            
        except Exception as e:
            # Log as warning instead of error since fallback to CBU is expected
            if "404" in str(e) and "bank.uz" in str(e):
                logger.warning(f"Currency {currency_code} not available on bank.uz, will use CBU fallback: {e}")
            else:
                logger.error(f"Error scraping bank.uz for {currency_code}: {e}")
            
        return None

    def get_cbu_rate(self, currency_code: str = 'USD') -> Optional[Dict]:
        """Get currency rate from Central Bank of Uzbekistan official JSON API"""
        try:
            currency_info = Config.get_currency_info(currency_code)
            if not currency_info:
                logger.error(f"Unsupported currency: {currency_code}")
                return None

            # Use the new official API endpoint
            url = f"{Config.CBU_API_URL}{currency_info['cbu_code']}/"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            data = response.json()

            if isinstance(data, list) and data:
                currency = data[0]
                rate = float(currency.get('Rate', 0))
                return {
                    'source': 'cbu.uz',
                    'currency': currency_code,
                    'currency_info': currency_info,
                    'central_bank_rate': rate,
                    'currency_name': currency.get('CcyNm_UZ', currency_info['name_uz']),
                    'timestamp': currency.get('Date', datetime.now().isoformat())
                }
            else:
                logger.error(f"No data returned from CBU API for {currency_code}")
        except Exception as e:
            logger.error(f"Error fetching CBU rate for {currency_code}: {e}")
        return None

    def get_exchange_api_rate(self, currency_code: str = 'USD') -> Optional[Dict]:
        """Get currency rate from exchange rate API"""
        try:
            currency_info = Config.get_currency_info(currency_code)
            if not currency_info:
                logger.error(f"Unsupported currency: {currency_code}")
                return None
            
            # Use USD as base and get UZS rate
            url = f"{Config.EXCHANGE_RATE_API_URL}USD"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if 'rates' in data:
                # For non-USD currencies, calculate rate via USD
                if currency_code == 'USD':
                    uzs_rate = data['rates'].get('UZS')
                    if uzs_rate:
                        return {
                            'source': 'exchange-api',
                            'currency': currency_code,
                            'currency_info': currency_info,
                            'central_bank_rate': uzs_rate,
                            'timestamp': datetime.now().isoformat()
                        }
                else:
                    # Get currency rate in USD and UZS rate in USD, then calculate
                    currency_to_usd = data['rates'].get(currency_code)
                    uzs_to_usd = data['rates'].get('UZS')
                    
                    if currency_to_usd and uzs_to_usd:
                        # Calculate currency to UZS rate
                        rate = uzs_to_usd / currency_to_usd
                        return {
                            'source': 'exchange-api',
                            'currency': currency_code,
                            'currency_info': currency_info,
                            'central_bank_rate': rate,
                            'timestamp': datetime.now().isoformat()
                        }
            
        except Exception as e:
            logger.error(f"Error fetching exchange API rate for {currency_code}: {e}")
            
        return None

    def get_all_rates(self, currency_code: str = 'USD') -> List[Dict]:
        """Get rates from all available sources for a specific currency"""
        rates = []
        
        # Get from bank.uz
        bank_uz_rate = self.get_bank_uz_rate(currency_code)
        if bank_uz_rate:
            rates.append(bank_uz_rate)
        
        # Get from CBU
        cbu_rate = self.get_cbu_rate(currency_code)
        if cbu_rate:
            rates.append(cbu_rate)
        
        # Get from exchange API as fallback
        if not rates:
            api_rate = self.get_exchange_api_rate(currency_code)
            if api_rate:
                rates.append(api_rate)
        
        return rates

    def get_best_rate(self, currency_code: str = 'USD') -> Optional[Dict]:
        """Get the most reliable rate available for a specific currency"""
        rates = self.get_all_rates(currency_code)
        
        if not rates:
            return None
        
        # Prefer CBU rate as it's the official central bank rate
        for rate in rates:
            if rate['source'] == 'cbu.uz':
                return rate
        
        # Then prefer bank.uz
        for rate in rates:
            if rate['source'] == 'bank.uz':
                return rate
        
        # Otherwise return the first available rate
        return rates[0]
    
    def get_multiple_currencies(self, currency_codes: List[str]) -> Dict[str, Optional[Dict]]:
        """Get rates for multiple currencies"""
        results = {}
        for currency_code in currency_codes:
            results[currency_code] = self.get_best_rate(currency_code)
        return results 

    def get_forecast_rates(self) -> Dict[str, Optional[Dict]]:
        """Get forecast rates for all supported currencies using tomorrow's date. If rates for tomorrow are available, return them."""
        from datetime import datetime, timedelta
        forecast_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        url = f"{Config.CBU_API_URL}all/{forecast_date}/"
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            data = response.json()
            if not isinstance(data, list):
                return {}
            forecast_rates = {}
            for currency in data:
                code = currency.get('Ccy')
                if code in Config.SUPPORTED_CURRENCIES:
                    rate = float(currency.get('Rate', 0))
                    forecast_rates[code] = {
                        'source': 'cbu.uz',
                        'currency': code,
                        'currency_info': Config.get_currency_info(code),
                        'central_bank_rate': rate,
                        'currency_name': currency.get('CcyNm_UZ', code),
                        'timestamp': currency.get('Date', forecast_date),
                        'is_forecast': True
                    }
            return forecast_rates
        except Exception as e:
            logger.error(f"Error fetching forecast rates: {e}")
            return {} 