from typing import Dict, Optional, List
from datetime import datetime
import json
from ..utils.config import Config

class MessageFormatter:
    """Format exchange rate data into informative messages"""
    
    @staticmethod
    def format_rate_update(current_rate: Dict, previous_rate: Optional[Dict] = None) -> str:
        """Format a rate update message"""
        if not current_rate:
            return "❌ Valyuta kursini olishda xatolik yuz berdi."
        
        currency_code = current_rate.get('currency', 'USD')
        currency_info = current_rate.get('currency_info', Config.get_currency_info(currency_code))
        source = current_rate.get('source', 'Unknown')
        central_rate = current_rate.get('central_bank_rate')
        timestamp = current_rate.get('timestamp', '')
        
        if not currency_info:
            currency_info = Config.get_currency_info('USD')  # fallback
        
        # Format timestamp
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            time_str = dt.strftime('%H:%M:%S')
            date_str = dt.strftime('%d.%m.%Y')
        except:
            time_str = "N/A"
            date_str = "N/A"
        
        # Build message
        emoji = currency_info['emoji']
        name_uz = currency_info['name_uz']
        symbol = currency_info['symbol']
        
        message = f"{emoji} **{name_uz}/UZS Valyuta Kursi**\n\n"
        message += f"🏦 **Markaziy Bank kursi:** {central_rate:,.0f} so'm ({symbol})\n"
        
        # Add bank rates if available
        if current_rate.get('buy_rate_min') and current_rate.get('buy_rate_max'):
            buy_min = current_rate['buy_rate_min']
            buy_max = current_rate['buy_rate_max']
            message += f"💵 **Sotib olish:** {buy_min:,.0f} - {buy_max:,.0f} so'm\n"
        
        if current_rate.get('sell_rate_min') and current_rate.get('sell_rate_max'):
            sell_min = current_rate['sell_rate_min']
            sell_max = current_rate['sell_rate_max']
            message += f"💸 **Sotish:** {sell_min:,.0f} - {sell_max:,.0f} so'm\n"
        
        # Add change information if previous rate exists
        change = None
        if previous_rate and previous_rate.get('central_bank_rate'):
            prev_rate = previous_rate['central_bank_rate']
            change = central_rate - prev_rate
            change_percent = (change / prev_rate) * 100
            
            if change > 0:
                emoji_change = "📈"
                direction = "oshdi"
                change_color = "🟢"
            elif change < 0:
                emoji_change = "📉" 
                direction = "tushdi"
                change_color = "🔴"
            else:
                emoji_change = "➡️"
                direction = "o'zgarmadi"
                change_color = "🟡"
            
            message += f"\n{emoji_change} **O'zgarish:** {change_color} {change:+,.0f} so'm ({change_percent:+.2f}%)\n"
            message += f"📊 **Avvalgi kurs:** {prev_rate:,.0f} so'm\n"
        
        message += f"\n🕐 **Vaqt:** {time_str}, {date_str}\n"
        message += f"📡 **Manba:** {source}\n"
        
        # Add context information
        message += f"\n💡 **Ma'lumot:**\n"
        if change and abs(change) > 50:
            message += f"• Kurs sezilarli o'zgarish ko'rsatdi\n"
        message += f"• Har 2 daqiqada yangilanadi\n"
        message += f"• /rate - joriy kursni ko'rish\n"
        
        return message
    
    @staticmethod
    def format_rate_request(rate_data: Dict) -> str:
        """Format response for rate request"""
        if not rate_data:
            return "❌ Hozirda valyuta kursini olishda xatolik. Qayta urinib ko'ring."
        
        return MessageFormatter.format_rate_update(rate_data)
    
    @staticmethod
    def format_multiple_rates(rates_data: Dict[str, Optional[Dict]], forecast_data: Dict[str, Optional[Dict]] = None) -> str:
        """Format multiple currency rates, optionally including forecast rates."""
        if not rates_data:
            return "❌ Hozirda valyuta kurslarini olishda xatolik."
        message = "💰 **Valyuta Kurslari**\n\n"
        for currency_code, rate_data in rates_data.items():
            if rate_data:
                currency_info = rate_data.get('currency_info', Config.get_currency_info(currency_code))
                if currency_info:
                    emoji = currency_info['emoji']
                    name_uz = currency_info['name_uz']
                    symbol = currency_info['symbol']
                    central_rate = rate_data.get('central_bank_rate', 0)
                    message += f"{emoji} **{name_uz}:** {central_rate:,.0f} so'm ({symbol})"
                    # Show forecast if available
                    if forecast_data and currency_code in forecast_data and forecast_data[currency_code]:
                        forecast_rate = forecast_data[currency_code]['central_bank_rate']
                        forecast_date = forecast_data[currency_code]['timestamp']
                        diff = forecast_rate - central_rate
                        diff_sign = '+' if diff > 0 else ('-' if diff < 0 else '')
                        diff_emoji = '🔺' if diff > 0 else ('🔻' if diff < 0 else '⏺️')
                        message += f"\n   └ 🕑 *Ertaga prognoz:* {forecast_rate:,.0f} so'm ({forecast_date}) {diff_emoji} {diff_sign}{abs(diff):,.0f}"
                    message += "\n"
                else:
                    message += f"❓ **{currency_code}:** Ma'lumot mavjud emas\n"
            else:
                message += f"❌ **{currency_code}:** Kurs olishda xatolik\n"
        try:
            dt = datetime.now()
            time_str = dt.strftime('%H:%M:%S, %d.%m.%Y')
            message += f"\n🕐 **Yangilangan:** {time_str}\n"
        except:
            pass
        message += f"\n💡 **Buyruqlar:**\n"
        message += f"• Muayyan valyuta kursi\n"
        message += f"• /rates - Barcha kurslar\n"
        return message
    
    @staticmethod
    def format_currency_selection() -> str:
        """Format currency selection message"""
        message = "💱 **Valyuta tanlang:**\n\n"
        
        currencies = Config.get_currency_list()
        for currency_code in currencies:
            currency_info = Config.get_currency_info(currency_code)
            if currency_info:
                emoji = currency_info['emoji']
                name_uz = currency_info['name_uz']
                name_en = currency_info['name_en']
                symbol = currency_info['symbol']
                
                message += f"{emoji} **{currency_code}** - {name_uz} ({name_en}) {symbol}\n"
        
        message += f"\n💡 **Foydalanish:**\n"
        message += f"• Quyidagi tugmalardan birini bosing\n"
        message += f"• Yoki /rate\\_USD kabi buyruq yozing\n"
        
        return message
    
    @staticmethod
    def format_notification_settings(user_id: int, user_notifications: dict) -> str:
        """Format notification settings message"""
        from ..utils.config import Config
        
        message = "🔔 **Xabar Sozlamalari**\n\n"
        message += "Qaysi valyutalar uchun xabar olishni xohlaysiz?\n\n"
        
        enabled_currencies = []
        disabled_currencies = []
        
        for currency_code in Config.get_currency_list():
            currency_info = Config.get_currency_info(currency_code)
            is_enabled = user_notifications.get(currency_code, False)
            
            if is_enabled:
                enabled_currencies.append(f"🔔 {currency_info['emoji']} {currency_info['name_uz']}")
            else:
                disabled_currencies.append(f"🔕 {currency_info['emoji']} {currency_info['name_uz']}")
        
        if enabled_currencies:
            message += "**Yoqilgan xabarlar:**\n"
            for currency in enabled_currencies:
                message += f"• {currency}\n"
            message += "\n"
        
        if disabled_currencies:
            message += "**O'chirilgan xabarlar:**\n"
            for currency in disabled_currencies:
                message += f"• {currency}\n"
            message += "\n"
        
        message += "💡 **Ma'lumot:**\n"
        message += "• Tugmalarni bosib xabarlarni yoqish/o'chirish mumkin\n"
        message += "• Bir nechta valyuta uchun xabar olish mumkin\n"
        message += "• Kurs sezilarli o'zgarganda xabar keladi\n"
        
        return message
    
    @staticmethod
    def format_error_message(error: str) -> str:
        """Format error message"""
        return f"⚠️ **Xatolik:** {error}\n\nQayta urinib ko'ring yoki keyinroq qayta urining."
    
    @staticmethod
    def format_startup_message() -> str:
        """Format bot startup message"""
        return (
            "🤖 **Multi-Currency Kurs Bot ishga tushdi!**\n\n"
            "📊 Men har 2 daqiqada valyuta kurslarini tekshiraman\n"
            "🔔 Kurs o'zgarganda sizga xabar beraman\n"
            "💬 /rates - barcha kurslar\n"
            "💱 /currencies - valyuta tanlash\n"
            "📱 /help - yordam\n\n"
            "✅ Bot faol ishlayapti..."
        )
    
    @staticmethod
    def format_help_message() -> str:
        """Format help message"""
        currencies = Config.get_currency_list()
        currency_list = ", ".join(currencies)
        
        return (
            "🤖 **Multi-Currency Kurs Bot**\n\n"
            "**Asosiy buyruqlar:**\n"
            "• /rates - Barcha valyuta kurslari\n"
            "• /currencies - Valyuta tanlash menyusi\n"
            "• /help - Bu yordam xabari\n"
            "• /status - Bot holati haqida ma'lumot\n\n"
            "**Valyuta buyruqlari:**\n"
            "• /rate\\_USD - Dollar kursi\n"
            "• /rate\\_EUR - Yevro kursi\n"
            "• /rate\\_RUB - Rubl kursi\n"
            "• Va hokazo...\n\n"
            "**Qo'llab-quvvatlanadigan valyutalar:**\n"
            f"• {currency_list}\n\n"
            "**Interfeys:**\n"
            "• 💰 Joriy Kurs - tanlangan valyuta kursi\n"
            "• 💱 Valyuta Tanlash - valyuta menyusi\n"
            "• 📊 Barcha Kurslar - bir vaqtda ko'rish\n"
            "• 🔔 Xabarlar - avtomatik xabarlarni boshqarish\n\n"
            "**Xususiyatlar:**\n"
            "• 📊 Har 2 daqiqada kurs tekshiriladi\n"
            "• 🔔 Avtomatik xabarlar (obuna/rad etish)\n"
            "• 🏦 Bank.uz va CBU.uz dan ma'lumot\n"
            "• 📈 O'zgarish dinamikasi va statistika\n\n"
            "**Ma'lumot manbalari:**\n"
            "• Bank.uz - kommersiya banklari kurslari\n"
            "• CBU.uz - O'zbekiston Markaziy Banki\n"
            "• Exchange API - qo'shimcha ma'lumotlar\n"
        )
    
    @staticmethod
    def format_status_message(is_running: bool, last_check: Optional[str] = None, 
                             last_rate: Optional[Dict] = None, selected_currency: str = 'USD') -> str:
        """Format bot status message"""
        status = "✅ Faol" if is_running else "❌ To'xtatilgan"
        currency_info = Config.get_currency_info(selected_currency)
        
        message = f"🤖 **Bot holati:** {status}\n\n"
        
        if currency_info:
            emoji = currency_info['emoji']
            name_uz = currency_info['name_uz']
            message += f"💱 **Tanlangan valyuta:** {emoji} {name_uz} ({selected_currency})\n"
        
        if last_check:
            try:
                dt = datetime.fromisoformat(last_check.replace('Z', '+00:00'))
                time_str = dt.strftime('%H:%M:%S, %d.%m.%Y')
                message += f"🕐 **Oxirgi tekshiruv:** {time_str}\n"
            except:
                message += f"🕐 **Oxirgi tekshiruv:** Ma'lum emas\n"
        
        if last_rate:
            rate = last_rate.get('central_bank_rate', 'N/A')
            source = last_rate.get('source', 'N/A')
            currency = last_rate.get('currency', selected_currency)
            message += f"💰 **Oxirgi kurs:** {rate:,.0f} so'm ({currency} - {source})\n"
        
        message += f"\n⚙️ **Sozlamalar:**\n"
        message += f"• Tekshiruv oralig'i: 2 daqiqa\n"
        message += f"• Manba: bank.uz, cbu.uz\n"
        message += f"• Qo'llab-quvvatlanadigan valyutalar: {len(Config.get_currency_list())}\n"
        
        return message 