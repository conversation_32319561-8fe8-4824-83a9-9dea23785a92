# 🚀 Deploy to Render.com - Step by Step Guide

## 🎯 Why Render.com?
- ✅ **Free tier** perfect for small bots
- ✅ **Easy GitHub integration**
- ✅ **Automatic deployments**
- ✅ **Built-in monitoring**
- ✅ **No credit card required** for free tier

---

## 📋 **Step 1: Prepare Your Code**

Your bot is ready! Files needed for Render:
- ✅ `bot.py` - Main bot file
- ✅ `requirements.txt` - Dependencies
- ✅ `render.yaml` - Render configuration
- ✅ `runtime.txt` - Python version
- ✅ `start.sh` - Start script

---

## 🔗 **Step 2: Push to GitHub**

1. **Create GitHub repository:**
   - Go to [github.com](https://github.com)
   - Click "New repository"
   - Name: `aha-valyuta-bot`
   - Make it **Public** (required for free tier)

2. **Upload your code:**
   ```bash
   # In your project directory
   git init
   git add .
   git commit -m "Initial commit: USD/UZS Exchange Rate Bot"
   git branch -M main
   git remote add origin https://github.com/YOUR_USERNAME/aha-valyuta-bot.git
   git push -u origin main
   ```

   **Or upload manually:**
   - Click "uploading an existing file"
   - Drag & drop all your bot files
   - Commit changes

---

## 🚀 **Step 3: Deploy on Render**

### **3.1 Create Render Account**
1. Go to [render.com](https://render.com)
2. Sign up with your GitHub account
3. Authorize Render to access your repositories

### **3.2 Create New Service**
1. Click **"New +"** → **"Web Service"**
2. Connect your repository: `aha-valyuta-bot`
3. Fill in settings:

   **Basic Settings:**
   - **Name:** `aha-valyuta-bot`
   - **Region:** `Oregon (US West)` (or closest to you)
   - **Branch:** `main`
   - **Root Directory:** (leave empty)
   - **Runtime:** `Python 3`
   - **Build Command:** `pip install -r requirements.txt`
   - **Start Command:** `python bot.py`

### **3.3 Configure Environment Variables**
Click **"Advanced"** and add these environment variables:

| Key | Value |
|-----|-------|
| `BOT_TOKEN` | `**********:AAH4dxVj-Hy6eC31Cw2vgWSB37nBxGRrZTI` |
| `CHAT_ID` | `*********` |
| `CHECK_INTERVAL` | `120` |

### **3.4 Select Plan**
- Choose **"Free"** plan
- Click **"Create Web Service"**

---

## 📺 **Step 4: Monitor Deployment**

1. **Watch the build logs:**
   - You'll see installation of dependencies
   - Bot starting up
   - Any errors will show here

2. **Successful deployment shows:**
   ```
   🚀 Starting USD/UZS Exchange Rate Bot on Render.com...
   Bot initialized, starting monitoring...
   Rate monitoring started
   ```

3. **Check your Telegram:**
   - You should receive startup message from [@aha_valyuta_bot](https://t.me/aha_valyuta_bot)

---

## ✅ **Step 5: Verify It's Working**

### **Test Commands:**
1. Message your bot: [@aha_valyuta_bot](https://t.me/aha_valyuta_bot)
2. Send: `/start`
3. Send: `/rate`
4. Send: `/status`

### **Check Logs on Render:**
- Go to your service dashboard
- Click **"Logs"** tab
- Should see rate checking every 2 minutes

---

## 🔧 **Render.com Free Tier Limits**

- ✅ **Runtime:** 750 hours/month (enough for 24/7)
- ✅ **Memory:** 512MB (plenty for this bot)
- ✅ **Bandwidth:** 100GB/month
- ⚠️ **Sleep:** Service sleeps after 15 min inactivity
- ⚠️ **Cold starts:** Takes 10-30 sec to wake up

**Note:** Your bot will work perfectly within these limits!

---

## 🛠️ **Troubleshooting**

### **Bot not starting:**
```bash
# Check build logs for:
- Missing dependencies
- Python version issues
- Environment variables
```

### **Rate fetching issues:**
```bash
# In logs, look for:
- Network connectivity errors
- bank.uz/cbu.uz access issues
- Rate parsing errors
```

### **Telegram connection issues:**
```bash
# Verify in logs:
- BOT_TOKEN is correct
- CHAT_ID is correct
- No Telegram API errors
```

### **Service sleeping:**
- Free tier sleeps after 15 min inactivity
- First message after sleep takes 10-30 sec
- This is normal for free tier

---

## 🔄 **Auto-Updates**

Every time you push to GitHub:
1. Render automatically rebuilds
2. Deploys new version
3. Restarts your bot
4. Zero downtime deployment!

To update:
```bash
# Make changes to your code
git add .
git commit -m "Update bot features"
git push
# Render automatically deploys!
```

---

## 📊 **Monitoring**

### **Render Dashboard:**
- Service status
- Resource usage
- Build history
- Live logs

### **Telegram Monitoring:**
- `/status` command shows bot health
- Automatic rate change notifications
- Error messages if issues occur

---

## 💰 **Upgrading (Optional)**

If you need 24/7 uptime without sleeping:
- **Starter Plan:** $7/month
- **No sleeping**
- **Better performance**
- **Custom domains**

---

## 🎉 **Success!**

Your bot is now:
- ✅ **Running 24/7** on Render.com
- ✅ **Monitoring USD/UZS rates** every 2 minutes
- ✅ **Sending notifications** on changes
- ✅ **Automatically updating** from GitHub

**Bot URL:** [@aha_valyuta_bot](https://t.me/aha_valyuta_bot)

Enjoy your automated exchange rate monitoring! 🚀 