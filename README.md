# USD/UZS Exchange Rate Telegram Bot

A Telegram bot that tracks USD to UZS (Uzbek Som) exchange rates and sends notifications when rates change.

## Features

- 📊 **Real-time Rate Tracking**: Monitors USD/UZS exchange rates every 2 minutes
- 🔔 **Automatic Notifications**: Sends messages when exchange rates change
- 💰 **Multiple Sources**: Fetches rates from bank.uz and CBU.uz (Central Bank of Uzbekistan)
- 🏦 **Comprehensive Data**: Shows central bank rates and commercial bank buy/sell rates
- 📈 **Change Analysis**: Displays rate changes with percentage and trend indicators
- 🇺🇿 **Uzbek Language**: All messages in Uzbek language for local users

## Commands

- `/start` - Welcome message and bot introduction
- `/rate` - Get current exchange rate
- `/help` - Show available commands and features
- `/status` - Display bot status and last update information

## Sources

The bot fetches exchange rate data from:

1. **bank.uz** - Commercial bank rates (buy/sell prices)
2. **cbu.uz** - Central Bank of Uzbekistan official rates

According to [bank.uz](https://bank.uz/uz/currency/dollar-ssha), the current USD rate shows:
- Central Bank rate
- Commercial bank buying rates (range)
- Commercial bank selling rates (range)

## Setup

### Prerequisites

- Python 3.8+
- Telegram <PERSON> (get from [@BotFather](https://t.me/botfather))
- Chat ID where you want to receive notifications

### Installation

1. **Clone or download this project**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Create environment file**:
   Create a `.env` file in the project directory:
   ```env
   BOT_TOKEN=your_telegram_bot_token_here
   CHAT_ID=your_chat_id_or_channel_id_here
   CHECK_INTERVAL=120
   ```

   **How to get these values:**
   
   - **BOT_TOKEN**: 
     1. Message [@BotFather](https://t.me/botfather) on Telegram
     2. Send `/newbot` command
     3. Follow instructions to create your bot
     4. Copy the token provided
   
   - **CHAT_ID**: 
     1. For personal notifications: Message [@userinfobot](https://t.me/userinfobot) to get your chat ID
     2. For channels: Add bot to channel as admin, then use channel @username or ID
     3. For groups: Add bot to group, then use group ID

4. **Run the bot**:
   ```bash
   python bot.py
   ```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `BOT_TOKEN` | Telegram Bot Token from BotFather | Required |
| `CHAT_ID` | Chat/Channel ID for notifications | Required |
| `CHECK_INTERVAL` | Rate check interval in seconds | 120 (2 minutes) |

### Rate Sources Configuration

The bot is configured to use multiple sources in `config.py`:

- `BANK_UZ_URL`: https://bank.uz/uz/currency/dollar-ssha
- `CBU_API_URL`: https://cbu.uz/uz/arkhiv-kursov-valyut/json/

## Usage

### Starting the Bot

1. Start the bot with `python bot.py`
2. The bot will automatically begin monitoring rates every 2 minutes
3. You'll receive a startup notification in your configured chat
4. Rate change notifications will be sent automatically

### Interacting with the Bot

Send these commands to the bot in Telegram:

- **Get current rate**: Send `/rate`
- **Get help**: Send `/help`  
- **Check bot status**: Send `/status`

### Sample Output

```
💰 USD/UZS Valyuta Kursi

🏦 Markaziy Bank kursi: 12,559 so'm
💵 Sotib olish: 12,500 - 12,555 so'm
💸 Sotish: 12,600 - 12,680 so'm

📈 O'zgarish: 🟢 +15 so'm (+0.12%)
📊 Avvalgi kurs: 12,544 so'm

🕐 Vaqt: 14:30:25, 15.12.2024
📡 Manba: cbu.uz

💡 Ma'lumot:
• Har 2 daqiqada yangilanadi
• /rate - joriy kursni ko'rish
```

## File Structure

```
kurs_bot/
├── bot.py              # Main bot application
├── rate_scraper.py     # Exchange rate scraping logic
├── message_formatter.py # Message formatting utilities
├── config.py           # Configuration settings
├── requirements.txt    # Python dependencies
├── README.md          # This file
├── .env               # Environment variables (create this)
└── last_rate.json     # Last rate cache (auto-generated)
```

## Technical Details

### Rate Monitoring

- Checks rates every 2 minutes (configurable)
- Compares new rates with previous rates
- Sends notifications only when rate changes by ≥1 som
- Handles multiple data sources with failover
- Persists last rate to survive bot restarts

### Error Handling

- Robust error handling for network issues
- Automatic retry with exponential backoff
- Graceful degradation when sources are unavailable
- Comprehensive logging for debugging

### Data Sources

1. **bank.uz**: Scrapes HTML to extract commercial bank rates
2. **cbu.uz**: Uses official JSON API for central bank rates

## Monitoring and Logs

The bot provides detailed logging:

```bash
2024-12-15 14:30:15 - __main__ - INFO - Starting USD/UZS Exchange Rate Bot...
2024-12-15 14:30:16 - __main__ - INFO - Bot initialized, starting monitoring...
2024-12-15 14:30:16 - __main__ - INFO - Rate monitoring started
2024-12-15 14:30:16 - __main__ - INFO - Checking for rate changes...
2024-12-15 14:30:17 - __main__ - INFO - Rate changed: 12544.0 -> 12559.0
2024-12-15 14:30:17 - __main__ - INFO - Rate update sent successfully
```

## Troubleshooting

### Common Issues

1. **Bot not starting**: Check if `BOT_TOKEN` is set correctly
2. **No notifications**: Verify `CHAT_ID` is correct and bot has permission to send messages
3. **Rate fetch errors**: Check internet connection and source website availability
4. **Permission errors**: Ensure bot is added to channel/group with proper permissions

### Getting Help

1. Check the logs for error messages
2. Verify all environment variables are set correctly
3. Test with `/rate` command to see if data fetching works
4. Use `/status` command to check bot health

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues and enhancement requests!

## Disclaimer

This bot is for informational purposes only. Exchange rates are fetched from public sources and may not reflect real-time market conditions. Always verify rates with official sources before making financial decisions. 