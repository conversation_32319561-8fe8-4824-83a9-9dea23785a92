#!/data/data/com.termux/files/usr/bin/bash
# Mobile bot updater with zero downtime
# Works on Termux (Android) and iSH (iOS)

echo "🔄 Starting zero-downtime bot update..."

# Check if bot is running
if ! tmux has-session -t botserver 2>/dev/null; then
    echo "❌ Bot is not running! Start it first with ./start_mobile.sh"
    exit 1
fi

# Backup current files
echo "💾 Creating backup..."
cp -r src src_backup_$(date +%Y%m%d_%H%M%S)

# Apply your code changes here
# For now, we'll use git pull if available, or manual file updates
if [ -d ".git" ]; then
    echo "📥 Pulling latest changes from git..."
    git pull origin main
else
    echo "📝 Manual update mode - make sure you've updated the files manually"
    echo "Updated files should include:"
    echo "  - src/core/rate_scraper.py"
    echo "  - src/utils/config.py"
    read -p "Press Enter when files are updated..."
fi

# Graceful restart
echo "🔄 Performing graceful restart..."

# Save current tmux session output
tmux capture-pane -t botserver -p > last_output.log

# Send graceful shutdown signal (Ctrl+C)
echo "⏸️ Stopping bot gracefully..."
tmux send-keys -t botserver C-c

# Wait for graceful shutdown (max 10 seconds)
echo "⏳ Waiting for graceful shutdown..."
for i in {1..10}; do
    sleep 1
    # Check if process is still running
    if ! tmux capture-pane -t botserver -p | grep -q "python3"; then
        echo "✅ Bot stopped gracefully"
        break
    fi
    echo "   Waiting... ($i/10)"
done

# Restart bot
echo "🚀 Restarting bot with new code..."
sleep 1
tmux send-keys -t botserver "python3 main.py" Enter

# Wait for startup
echo "⏳ Waiting for bot to start..."
sleep 3

# Check if bot started successfully
if tmux capture-pane -t botserver -p | grep -q "Bot started successfully\|INFO"; then
    echo "✅ Bot updated and restarted successfully!"
    echo "📊 New features applied:"
    echo "   • Fixed CHF currency error"
    echo "   • Improved error handling"
    echo "   • Optimized for CBU source only"
else
    echo "⚠️ Bot might have issues starting. Check logs with:"
    echo "   tmux attach -t botserver"
fi

echo ""
echo "🎉 Update complete!"
echo "📱 Useful commands:"
echo "   tmux attach -t botserver     # View bot logs"
echo "   ./check_mobile.sh           # Check bot status" 